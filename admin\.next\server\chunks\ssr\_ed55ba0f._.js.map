{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/components/ui/label.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as LabelPrimitive from '@radix-ui/react-label';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Label({ className, ...props }: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        'flex items-center mb-2 gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Label };\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAyD;IACtF,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4NACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn('leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return <div data-slot=\"card-content\" className={cn('px-6', className)} {...props} />;\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kEAAkE;QAC/E,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBAAO,8OAAC;QAAI,aAAU;QAAe,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAa,GAAG,KAAK;;;;;;AAClF;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/components/ui/table.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Table({ className, ...props }: React.ComponentProps<'table'>) {\r\n  return (\r\n    <div data-slot=\"table-container\" className=\"relative w-full overflow-x-auto\">\r\n      <table\r\n        data-slot=\"table\"\r\n        className={cn('w-full caption-bottom text-sm', className)}\r\n        {...props}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction TableHeader({ className, ...props }: React.ComponentProps<'thead'>) {\r\n  return <thead data-slot=\"table-header\" className={cn('[&_tr]:border-b', className)} {...props} />;\r\n}\r\n\r\nfunction TableBody({ className, ...props }: React.ComponentProps<'tbody'>) {\r\n  return (\r\n    <tbody\r\n      data-slot=\"table-body\"\r\n      className={cn('[&_tr:last-child]:border-0', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableFooter({ className, ...props }: React.ComponentProps<'tfoot'>) {\r\n  return (\r\n    <tfoot\r\n      data-slot=\"table-footer\"\r\n      className={cn('bg-muted/50 border-t font-medium [&>tr]:last:border-b-0', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableRow({ className, ...props }: React.ComponentProps<'tr'>) {\r\n  return (\r\n    <tr\r\n      data-slot=\"table-row\"\r\n      className={cn(\r\n        'hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableHead({ className, ...props }: React.ComponentProps<'th'>) {\r\n  return (\r\n    <th\r\n      data-slot=\"table-head\"\r\n      className={cn(\r\n        'text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableCell({ className, ...props }: React.ComponentProps<'td'>) {\r\n  return (\r\n    <td\r\n      data-slot=\"table-cell\"\r\n      className={cn(\r\n        'p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableCaption({ className, ...props }: React.ComponentProps<'caption'>) {\r\n  return (\r\n    <caption\r\n      data-slot=\"table-caption\"\r\n      className={cn('text-muted-foreground mt-4 text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Table, TableHeader, TableBody, TableFooter, TableHead, TableRow, TableCell, TableCaption };\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QAAI,aAAU;QAAkB,WAAU;kBACzC,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBAAO,8OAAC;QAAM,aAAU;QAAe,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;AAC/F;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2DAA2D;QACxE,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAwC;IAC5E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/app-components/dataTable.tsx"], "sourcesContent": ["import {\r\n  useReactTable,\r\n  getCoreRowModel,\r\n  ColumnDef,\r\n  flexRender,\r\n} from \"@tanstack/react-table\";\r\n\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\";\r\n\r\ninterface DataTableProps<T> {\r\n  columns: ColumnDef<T>[];\r\n  data: T[];\r\n  isLoading?: boolean;\r\n  getRowClassName?: (row: T, index: number) => string;\r\n}\r\n\r\nexport function DataTable<T>({\r\n  columns,\r\n  data,\r\n  isLoading,\r\n  getRowClassName,\r\n}: DataTableProps<T>) {\r\n  const table = useReactTable({\r\n    data,\r\n    columns,\r\n    getCoreRowModel: getCoreRowModel(),\r\n  });\r\n\r\n  return (\r\n    <div>\r\n      {isLoading ? (\r\n        <div className=\"flex justify-center items-center h-64\">\r\n          <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"></div>\r\n        </div>\r\n      ) : (\r\n        <div className=\"rounded-md border\">\r\n          <Table>\r\n            <TableHeader className=\"sticky top-0 bg-muted z-10\">\r\n              {table.getHeaderGroups().map((headerGroup) => (\r\n                <TableRow key={headerGroup.id}>\r\n                  {headerGroup.headers.map((header) => (\r\n                    <TableHead key={header.id}>\r\n                      {header.isPlaceholder\r\n                        ? null\r\n                        : flexRender(\r\n                            header.column.columnDef.header,\r\n                            header.getContext()\r\n                          )}\r\n                    </TableHead>\r\n                  ))}\r\n                </TableRow>\r\n              ))}\r\n            </TableHeader>\r\n            <TableBody>\r\n              {table.getRowModel().rows?.length ? (\r\n                table.getRowModel().rows.map((row, index) => {\r\n                  const customClassName = getRowClassName\r\n                    ? getRowClassName(row.original, index)\r\n                    : \"\";\r\n                  return (\r\n                    <TableRow\r\n                      key={row.id}\r\n                      className={`hover:bg-gray-50 ${customClassName}`}\r\n                    >\r\n                      {row.getVisibleCells().map((cell) => (\r\n                        <TableCell key={cell.id}>\r\n                          {flexRender(\r\n                            cell.column.columnDef.cell,\r\n                            cell.getContext()\r\n                          )}\r\n                        </TableCell>\r\n                      ))}\r\n                    </TableRow>\r\n                  );\r\n                })\r\n              ) : (\r\n                <TableRow>\r\n                  <TableCell\r\n                    colSpan={columns.length}\r\n                    className=\"text-center py-4\"\r\n                  >\r\n                    No data found.\r\n                  </TableCell>\r\n                </TableRow>\r\n              )}\r\n            </TableBody>\r\n          </Table>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAOA;;;;AAgBO,SAAS,UAAa,EAC3B,OAAO,EACP,IAAI,EACJ,SAAS,EACT,eAAe,EACG;IAClB,MAAM,QAAQ,CAAA,GAAA,sLAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B;QACA;QACA,iBAAiB,CAAA,GAAA,qKAAA,CAAA,kBAAe,AAAD;IACjC;IAEA,qBACE,8OAAC;kBACE,0BACC,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;iCAGjB,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;;kCACJ,8OAAC,iIAAA,CAAA,cAAW;wBAAC,WAAU;kCACpB,MAAM,eAAe,GAAG,GAAG,CAAC,CAAC,4BAC5B,8OAAC,iIAAA,CAAA,WAAQ;0CACN,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,uBACxB,8OAAC,iIAAA,CAAA,YAAS;kDACP,OAAO,aAAa,GACjB,OACA,CAAA,GAAA,sLAAA,CAAA,aAAU,AAAD,EACP,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;uCALT,OAAO,EAAE;;;;;+BAFd,YAAY,EAAE;;;;;;;;;;kCAcjC,8OAAC,iIAAA,CAAA,YAAS;kCACP,MAAM,WAAW,GAAG,IAAI,EAAE,SACzB,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK;4BACjC,MAAM,kBAAkB,kBACpB,gBAAgB,IAAI,QAAQ,EAAE,SAC9B;4BACJ,qBACE,8OAAC,iIAAA,CAAA,WAAQ;gCAEP,WAAW,CAAC,iBAAiB,EAAE,iBAAiB;0CAE/C,IAAI,eAAe,GAAG,GAAG,CAAC,CAAC,qBAC1B,8OAAC,iIAAA,CAAA,YAAS;kDACP,CAAA,GAAA,sLAAA,CAAA,aAAU,AAAD,EACR,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;uCAHH,KAAK,EAAE;;;;;+BAJpB,IAAI,EAAE;;;;;wBAajB,mBAEA,8OAAC,iIAAA,CAAA,WAAQ;sCACP,cAAA,8OAAC,iIAAA,CAAA,YAAS;gCACR,SAAS,QAAQ,MAAM;gCACvB,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}, {"offset": {"line": 364, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/app-components/pagination.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport {\r\n  ChevronsLeftIcon,\r\n  ChevronsRightIcon,\r\n  ChevronLeftIcon,\r\n  ChevronRightIcon,\r\n} from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\ninterface PaginationProps {\r\n  page: number;\r\n  totalPages: number;\r\n  setPage: (page: number) => void;\r\n  entriesText?: string;\r\n}\r\n\r\nconst pagination = ({\r\n  page,\r\n  totalPages,\r\n  setPage,\r\n  entriesText,\r\n}: PaginationProps) => {\r\n  return (\r\n    <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 px-4 py-2\">\r\n      {entriesText && (\r\n        <div className=\"text-sm text-muted-foreground\">{entriesText}</div>\r\n      )}\r\n\r\n      <div className=\"flex items-center gap-2\">\r\n        <Button\r\n          variant=\"outline\"\r\n          size=\"icon\"\r\n          onClick={() => setPage(1)}\r\n          disabled={page === 1 || page > totalPages}\r\n        >\r\n          <ChevronsLeftIcon className=\"h-4 w-4\" />\r\n        </Button>\r\n        <Button\r\n          variant=\"outline\"\r\n          size=\"icon\"\r\n          onClick={() => setPage(page - 1)}\r\n          disabled={page === 1 || page > totalPages}\r\n        >\r\n          <ChevronLeftIcon className=\"h-4 w-4\" />\r\n        </Button>\r\n        <span className=\"text-sm\">\r\n          Page {page} of {totalPages}\r\n        </span>\r\n        <Button\r\n          variant=\"outline\"\r\n          size=\"icon\"\r\n          onClick={() => setPage(page + 1)}\r\n          disabled={page === totalPages || page > totalPages}\r\n        >\r\n          <ChevronRightIcon className=\"h-4 w-4\" />\r\n        </Button>\r\n        <Button\r\n          variant=\"outline\"\r\n          size=\"icon\"\r\n          onClick={() => setPage(totalPages)}\r\n          disabled={page === totalPages || page > totalPages}\r\n        >\r\n          <ChevronsRightIcon className=\"h-4 w-4\" />\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default pagination;\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAMA;AARA;;;;AAiBA,MAAM,aAAa,CAAC,EAClB,IAAI,EACJ,UAAU,EACV,OAAO,EACP,WAAW,EACK;IAChB,qBACE,8OAAC;QAAI,WAAU;;YACZ,6BACC,8OAAC;gBAAI,WAAU;0BAAiC;;;;;;0BAGlD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,QAAQ;wBACvB,UAAU,SAAS,KAAK,OAAO;kCAE/B,cAAA,8OAAC,0NAAA,CAAA,mBAAgB;4BAAC,WAAU;;;;;;;;;;;kCAE9B,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,QAAQ,OAAO;wBAC9B,UAAU,SAAS,KAAK,OAAO;kCAE/B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;4BAAC,WAAU;;;;;;;;;;;kCAE7B,8OAAC;wBAAK,WAAU;;4BAAU;4BAClB;4BAAK;4BAAK;;;;;;;kCAElB,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,QAAQ,OAAO;wBAC9B,UAAU,SAAS,cAAc,OAAO;kCAExC,cAAA,8OAAC,0NAAA,CAAA,mBAAgB;4BAAC,WAAU;;;;;;;;;;;kCAE9B,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,QAAQ;wBACvB,UAAU,SAAS,cAAc,OAAO;kCAExC,cAAA,8OAAC,4NAAA,CAAA,oBAAiB;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKvC;uCAEe", "debugId": null}}, {"offset": {"line": 493, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/services/leaderbordApi.ts"], "sourcesContent": ["import { axiosInstance } from \"@/lib/axios\";\r\n\r\ninterface MockExamLeaderboardFilters {\r\n  firstName?: string;\r\n  lastName?: string;\r\n  email?: string;\r\n  contact?: string;\r\n  score?: string;\r\n  coins?:string;\r\n  date?: string;  \r\n}\r\n\r\nexport const getMockExamLeaderboard = async (\r\n  timeframe: string,\r\n  page: number = 1,\r\n  limit: number = 10,\r\n  filters: MockExamLeaderboardFilters = {}\r\n): Promise<any> => {\r\n  try {\r\n    const params = new URLSearchParams({\r\n      page: page.toString(),\r\n      limit: limit.toString(),\r\n    });\r\n   \r\n    if (filters.firstName?.trim()) params.append('firstName', filters.firstName.trim());\r\n    if (filters.lastName?.trim()) params.append('lastName', filters.lastName.trim());\r\n    if (filters.email?.trim()) params.append('email', filters.email.trim());\r\n    if (filters.score?.trim()) params.append('score', filters.score.trim());\r\n    if (filters.coins?.trim()) params.append('coins', filters.coins.trim());\r\n    if (filters.date?.trim()) params.append('date', filters.date.trim()); // Added date filter\r\n\r\n    const response = await axiosInstance.get(\r\n      `/mock-exam-leaderboard/leaderboard/${timeframe}?${params.toString()}`,\r\n      {\r\n        headers: {\r\n          \"Server-Select\": \"uwhizServer\",\r\n        },\r\n      }\r\n    );\r\n    return { success: true, data: response.data };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to get mock exam leaderboard data: ${\r\n        error.response?.data?.error || error.message\r\n      }`,\r\n    };\r\n  }\r\n};\r\nexport default MockExamLeaderboardFilters"], "names": [], "mappings": ";;;AAAA;;AAYO,MAAM,yBAAyB,OACpC,WACA,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,UAAsC,CAAC,CAAC;IAExC,IAAI;QACF,MAAM,SAAS,IAAI,gBAAgB;YACjC,MAAM,KAAK,QAAQ;YACnB,OAAO,MAAM,QAAQ;QACvB;QAEA,IAAI,QAAQ,SAAS,EAAE,QAAQ,OAAO,MAAM,CAAC,aAAa,QAAQ,SAAS,CAAC,IAAI;QAChF,IAAI,QAAQ,QAAQ,EAAE,QAAQ,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ,CAAC,IAAI;QAC7E,IAAI,QAAQ,KAAK,EAAE,QAAQ,OAAO,MAAM,CAAC,SAAS,QAAQ,KAAK,CAAC,IAAI;QACpE,IAAI,QAAQ,KAAK,EAAE,QAAQ,OAAO,MAAM,CAAC,SAAS,QAAQ,KAAK,CAAC,IAAI;QACpE,IAAI,QAAQ,KAAK,EAAE,QAAQ,OAAO,MAAM,CAAC,SAAS,QAAQ,KAAK,CAAC,IAAI;QACpE,IAAI,QAAQ,IAAI,EAAE,QAAQ,OAAO,MAAM,CAAC,QAAQ,QAAQ,IAAI,CAAC,IAAI,KAAK,oBAAoB;QAE1F,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CACtC,CAAC,mCAAmC,EAAE,UAAU,CAAC,EAAE,OAAO,QAAQ,IAAI,EACtE;YACE,SAAS;gBACP,iBAAiB;YACnB;QACF;QAEF,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI;QAAC;IAC9C,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,0CAA0C,EAChD,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,EAC5C;QACJ;IACF;AACF", "debugId": null}}, {"offset": {"line": 532, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/public/Streak.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\ninterface StreakBadgeProps {\r\n  count: number;\r\n}\r\n\r\nconst StreakBadge: React.FC<StreakBadgeProps> = ({ count }) => {\r\n  return (\r\n    <svg\r\n      className=\"h-10 w-10 sm:h-12 sm:w-12\"\r\n      viewBox=\"0 0 1550 1808\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      {/* Background Shape */}\r\n      <path\r\n        d=\"M574.764 165.821C692.4 74.0944 857.6 74.0944 975.236 165.821L1262.05 386.652C1354.36 459.21 1407.96 571.58 1407.96 690.458V1117.54C1407.96 1236.42 1354.36 1348.79 1262.05 1421.35L975.236 1642.18C857.6 1733.91 692.4 1733.91 574.764 1642.18L287.951 1421.35C195.639 1348.79 142.037 1236.42 142.037 1117.54V690.458C142.037 571.58 195.639 459.21 287.951 386.652L574.764 165.821Z\"\r\n        fill=\"#FDFEF9\"\r\n      />\r\n\r\n      {/* Optional Gradient or Mask Section */}\r\n      <mask id=\"mask0\" maskUnits=\"userSpaceOnUse\" x=\"71\" y=\"180\" width=\"1408\" height=\"1484\">\r\n        <path\r\n          d=\"M574.315 248.124C692.703 157.412 857.297 157.412 975.685 248.124L1262.5 468.955C1354.81 541.513 1408.41 653.884 1408.41 772.762V1199.84C1408.41 1318.72 1354.81 1431.09 1262.5 1503.65L975.685 1724.48C857.297 1815.19 692.703 1815.19 574.315 1724.48L287.502 1503.65C195.19 1431.09 141.588 1318.72 141.588 1199.84V772.762C141.588 653.884 195.19 541.513 287.502 468.955L574.315 248.124Z\"\r\n          fill=\"#CCCCCC\"\r\n        />\r\n      </mask>\r\n\r\n      <g mask=\"url(#mask0)\">\r\n        <rect x=\"48\" y=\"146\" width=\"1454\" height=\"821\" fill=\"#CCCCCC\" />\r\n      </g>\r\n\r\n      {/* Decorative Paths (static) */}\r\n      <path d=\"M658.6 1303.6C658.6 1257.73 713.743 1221.6 783 1221.6C852.257 1221.6 907.4 1257.73 907.4 1303.6C907.4 1328.13 861.847 1343.6 783 1343.6C704.153 1343.6 658.6 1328.13 658.6 1303.6Z\" fill=\"#CCCCCC\" />\r\n      <path d=\"M771.553 390C809.071 390 837.553 418.482 837.553 456C837.553 475.694 829.359 495.486 814.097 508.145L776.947 539.162C772.316 543.05 766.79 545.197 761.062 545.197C755.335 545.197 749.809 543.05 745.178 539.162L707.303 508.09C691.856 495.367 683.553 475.418 683.553 455.5C683.553 417.982 712.035 390 749.553 390H771.553Z\" fill=\"white\" />\r\n      <path d=\"M774.5 828C743.884 828 719 852.884 719 883.5C719 914.116 743.884 939 774.5 939C805.116 939 830 914.116 830 883.5C830 852.884 805.116 828 774.5 828Z\" fill=\"#CCCCCC\" />\r\n      <path d=\"M1043.07 450.583L1028.87 437.844C1028.55 437.56 1028.07 437.593 1027.79 437.917L1002.56 467.325C1002.28 467.648 1002.31 468.13 1002.64 468.413L1016.84 481.152C1017.16 481.436 1017.64 481.403 1017.93 481.079L1043.16 451.671C1043.44 451.348 1043.41 450.866 1043.07 450.583Z\" fill=\"white\" />\r\n      <path d=\"M1044.49 451.93C1044.25 451.709 1043.87 451.743 1043.66 452.012L1018.13 484.509C1017.92 484.778 1017.95 485.158 1018.22 485.371L1020.46 487.151C1020.73 487.365 1021.11 487.331 1021.32 487.062L1046.85 454.565C1047.06 454.296 1047.03 453.916 1046.76 453.702L1044.49 451.93Z\" fill=\"white\" />\r\n      <path d=\"M1044.53 453.612L1018.39 486.109C1018.17 486.385 1018.2 486.794 1018.46 487.033L1023.27 491.522C1023.53 491.76 1023.95 491.73 1024.18 491.454L1050.32 458.957C1050.54 458.681 1050.51 458.272 1050.25 458.033L1045.44 453.544C1045.18 453.306 1044.77 453.336 1044.53 453.612Z\" fill=\"white\" />\r\n      <text\r\n        x=\"50%\"\r\n        y=\"80%\"\r\n        textAnchor=\"middle\"\r\n        fill=\"#222\"\r\n        fontSize=\"300\"\r\n        fontWeight=\"bold\"\r\n        fontFamily=\"Arial, sans-serif\"\r\n      >\r\n        {count}\r\n      </text>\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport default StreakBadge;\r\n"], "names": [], "mappings": ";;;;;AAMA,MAAM,cAA0C,CAAC,EAAE,KAAK,EAAE;IACxD,qBACE,8OAAC;QACC,WAAU;QACV,SAAQ;QACR,MAAK;QACL,OAAM;;0BAGN,8OAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAIP,8OAAC;gBAAK,IAAG;gBAAQ,WAAU;gBAAiB,GAAE;gBAAK,GAAE;gBAAM,OAAM;gBAAO,QAAO;0BAC7E,cAAA,8OAAC;oBACC,GAAE;oBACF,MAAK;;;;;;;;;;;0BAIT,8OAAC;gBAAE,MAAK;0BACN,cAAA,8OAAC;oBAAK,GAAE;oBAAK,GAAE;oBAAM,OAAM;oBAAO,QAAO;oBAAM,MAAK;;;;;;;;;;;0BAItD,8OAAC;gBAAK,GAAE;gBAAqL,MAAK;;;;;;0BAClM,8OAAC;gBAAK,GAAE;gBAAkU,MAAK;;;;;;0BAC/U,8OAAC;gBAAK,GAAE;gBAAsJ,MAAK;;;;;;0BACnK,8OAAC;gBAAK,GAAE;gBAAkR,MAAK;;;;;;0BAC/R,8OAAC;gBAAK,GAAE;gBAAkR,MAAK;;;;;;0BAC/R,8OAAC;gBAAK,GAAE;gBAAiR,MAAK;;;;;;0BAC9R,8OAAC;gBACC,GAAE;gBACF,GAAE;gBACF,YAAW;gBACX,MAAK;gBACL,UAAS;gBACT,YAAW;gBACX,YAAW;0BAEV;;;;;;;;;;;;AAIT;uCAEe", "debugId": null}}, {"offset": {"line": 666, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/components/ui/badgedisplay.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport StreakBadge from \"../../../public/Streak\";\r\nimport Image from \"next/image\";\r\n\r\ninterface Badge {\r\n  badgeType: string;\r\n  badgeSrc: string;\r\n  badgeAlt: string;\r\n  count?: number;\r\n}\r\n\r\ninterface BadgeDisplayProps {\r\n  badge: {\r\n    streakCount: number;\r\n    badges: Badge[];\r\n    badgeType: string | null;\r\n    badgeSrc: string | null;\r\n    badgeAlt: string | null;\r\n  } | null;\r\n}\r\n\r\nexport default function BadgeDisplay({ badge }: BadgeDisplayProps) {\r\n  if (!badge?.badges?.length) return null;\r\n\r\n  return (\r\n    <div className=\"flex gap-3 mt-2\">\r\n      {badge.badges.map((b, index) => (\r\n        <motion.div\r\n          key={index}\r\n          className=\"relative\"\r\n          initial={{ opacity: 0, scale: 0.9 }}\r\n          animate={{ opacity: 1, scale: 1 }}\r\n          transition={{ duration: 0.4, delay: index * 0.1 }}\r\n        >\r\n          {b.badgeType === \"DailyStreak\" ? (\r\n            <StreakBadge count={b.count ?? 0} />\r\n          ) : (\r\n            <Image\r\n              src={b.badgeSrc ?? \"/placeholder.png\"}\r\n              alt={b.badgeAlt ?? \"Badge\"}\r\n              width={48}\r\n              height={48}\r\n              className=\"object-contain sm:w-12 sm:h-12 w-10 h-10\"\r\n            />\r\n          )}\r\n        </motion.div>\r\n      ))}\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAuBe,SAAS,aAAa,EAAE,KAAK,EAAqB;IAC/D,IAAI,CAAC,OAAO,QAAQ,QAAQ,OAAO;IAEnC,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAET,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAI;gBAClC,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBAChC,YAAY;oBAAE,UAAU;oBAAK,OAAO,QAAQ;gBAAI;0BAE/C,EAAE,SAAS,KAAK,8BACf,8OAAC,iHAAA,CAAA,UAAW;oBAAC,OAAO,EAAE,KAAK,IAAI;;;;;yCAE/B,8OAAC,6HAAA,CAAA,UAAK;oBACJ,KAAK,EAAE,QAAQ,IAAI;oBACnB,KAAK,EAAE,QAAQ,IAAI;oBACnB,OAAO;oBACP,QAAQ;oBACR,WAAU;;;;;;eAdT;;;;;;;;;;AAqBf", "debugId": null}}, {"offset": {"line": 730, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/services/dailyquizTerminationlogApi.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\n\r\nexport const getTerminatedStudentLogs = async (studentId: string) => {\r\n  try {\r\n    const response = await axiosInstance.get(`/mock-exam-terminate/${studentId}`, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n\r\n    return {\r\n      success: true,\r\n      data: response.data.data,\r\n    };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to fetch termination logs: ${error.response?.data?.message || error.message}`,\r\n    };\r\n  }\r\n};"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,2BAA2B,OAAO;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,WAAW,EAAE;YAC5E,SAAS;gBACP,iBAAiB;YACnB;QACF;QAEA,OAAO;YACL,SAAS;YACT,MAAM,SAAS,IAAI,CAAC,IAAI;QAC1B;IACF,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,kCAAkC,EAAE,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAAE;QAC9F;IACF;AACF", "debugId": null}}, {"offset": {"line": 759, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/admin/src/app/%28examDashboard%29/dailyquiz-leaderbord/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, useMemo } from \"react\";\r\nimport { ColumnDef } from \"@tanstack/react-table\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { DataTable } from \"@/app-components/dataTable\";\r\nimport Pagination from \"@/app-components/pagination\";\r\nimport MockExamLeaderboardFilters, { getMockExamLeaderboard } from \"@/services/leaderbordApi\";\r\nimport BadgeDisplay from \"@/components/ui/badgedisplay\";\r\nimport Image from 'next/image';\r\nimport { AlertTriangle, RefreshCw, X, Filter, CalendarIcon } from \"lucide-react\";\r\nimport { format } from \"date-fns\";\r\nimport { getTerminatedStudentLogs } from \"@/services/dailyquizTerminationlogApi\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\ninterface LeaderboardUser {\r\n    rank: number;\r\n    studentId: string;\r\n    score: number;\r\n    coinEarnings: number;\r\n    streakCount: number;\r\n    firstName: string | null;\r\n    lastName: string | null;\r\n    email: string | null;\r\n    contact?: string | null;\r\n    badge: {\r\n        streakCount: number;\r\n        badges: {\r\n            badgeType: string;\r\n            badgeSrc: string;\r\n            badgeAlt: string;\r\n            count?: number;\r\n        }[];\r\n        badgeType: string | null;\r\n        badgeSrc: string | null;\r\n        badgeAlt: string | null;\r\n    };\r\n    createdAt: string;\r\n}\r\n\r\ninterface LeaderboardData {\r\n    data: LeaderboardUser[];\r\n    total: number;\r\n}\r\n\r\ninterface TerminationLog {\r\n    id: string;\r\n    reason: string;\r\n    createdAt: string;\r\n}\r\n\r\nconst PAGE_SIZE = 10;\r\n\r\nexport default function SimpleLeaderboardTable() {\r\n    const [leaderboardData, setLeaderboardData] = useState<LeaderboardData>({\r\n        data: [],\r\n        total: 0,\r\n    });\r\n    const [currentPage, setCurrentPage] = useState(1);\r\n    const [loading, setLoading] = useState(false);\r\n    const [error, setError] = useState<string | null>(null);\r\n    const [showFilters, setShowFilters] = useState(false);\r\n    const router = useRouter();\r\n\r\n    const [filters, setFilters] = useState<MockExamLeaderboardFilters>({\r\n        firstName: '',\r\n        lastName: '',\r\n        email: '',\r\n        score: '',\r\n        coins: '',\r\n        date: '',\r\n\r\n    });\r\n\r\n    const [appliedFilters, setAppliedFilters] = useState<MockExamLeaderboardFilters>({});\r\n    const [selectedStudentForTermination, setSelectedStudentForTermination] = useState<LeaderboardUser | null>(null);\r\n    const [terminationLogs, setTerminationLogs] = useState<TerminationLog[]>([]);\r\n    const [terminationLogsLoading, setTerminationLogsLoading] = useState(false);\r\n\r\n    const timeframe = \"all-time\";\r\n\r\n    const handleSearch = () => {\r\n        console.log('Applying filters:', filters);\r\n        setCurrentPage(1);\r\n        setAppliedFilters({ ...filters });\r\n    };\r\n    const handleStudentDailyQuizeResult = (studentId: string, firstName: string | null, lastName: string | null) => {\r\n        const firstNameParam = firstName || '';\r\n        const lastNameParam = lastName || '';\r\n        router.push(`/student-dailyquize-result/${studentId}?firstName=${encodeURIComponent(firstNameParam)}&lastName=${encodeURIComponent(lastNameParam)}`);\r\n    };\r\n    const handleResetFilters = () => {\r\n        console.log('Resetting filters');\r\n        const resetFilters: MockExamLeaderboardFilters = {\r\n            firstName: '',\r\n            lastName: '',\r\n            email: '',\r\n            score: '',\r\n            coins: '',\r\n            date: '',\r\n\r\n        };\r\n        setFilters(resetFilters);\r\n        setAppliedFilters({});\r\n        setCurrentPage(1);\r\n    };\r\n\r\n    const handleFilterChange = (field: keyof MockExamLeaderboardFilters, value: string) => {\r\n        setFilters((prev) => ({\r\n            ...prev,\r\n            [field]: value\r\n        }));\r\n    };\r\n    const handleDateChange = (dateValue: string) => {\r\n        setFilters((prev) => ({\r\n            ...prev,\r\n            date: dateValue\r\n        }));\r\n    };\r\n    const getBadgeIcon = (coinEarnings: number) => {\r\n        if (coinEarnings >= 100 && coinEarnings <= 499) {\r\n            return '/scholer.svg';\r\n        } else if (coinEarnings >= 500 && coinEarnings <= 999) {\r\n            return '/Mastermind.svg';\r\n        } else if (coinEarnings >= 1000) {\r\n            return '/Achiever.svg';\r\n        }\r\n        return null;\r\n    };\r\n\r\n    const getBadgeAlt = (coinEarnings: number) => {\r\n        if (coinEarnings >= 100 && coinEarnings <= 499) {\r\n            return 'Scholar Badge';\r\n        } else if (coinEarnings >= 500 && coinEarnings <= 999) {\r\n            return 'Mastermind Badge';\r\n        } else if (coinEarnings >= 1000) {\r\n            return 'Achiever Badge';\r\n        }\r\n        return '';\r\n    };\r\n\r\n    const getStudentName = (user: LeaderboardUser) => {\r\n        if (user.firstName && user.lastName) {\r\n            return `${user.firstName} ${user.lastName}`;\r\n        }\r\n        return user.email || `Student ${user.studentId}`;\r\n    };\r\n\r\n    const fetchStudentTerminationLogs = async (student: LeaderboardUser) => {\r\n        setSelectedStudentForTermination(student);\r\n        setTerminationLogsLoading(true);\r\n        setTerminationLogs([]);\r\n\r\n        try {\r\n            const response = await getTerminatedStudentLogs(student.studentId);\r\n\r\n            if (response.success) {\r\n                setTerminationLogs(response.data || []);\r\n            } else {\r\n                console.error(\"Failed to fetch termination logs:\", response.error);\r\n                setTerminationLogs([]);\r\n            }\r\n        } catch (error) {\r\n            console.error(\"Error fetching termination logs:\", error);\r\n            setTerminationLogs([]);\r\n        } finally {\r\n            setTerminationLogsLoading(false);\r\n        }\r\n    };\r\n\r\n    const fetchLeaderboardData = async () => {\r\n        setLoading(true);\r\n        setError(null);\r\n\r\n        try {\r\n            const response = await getMockExamLeaderboard(timeframe, currentPage, PAGE_SIZE, appliedFilters);\r\n\r\n            if (response.success) {\r\n                setLeaderboardData(response.data);\r\n            } else {\r\n                setError(response.error || \"Failed to fetch leaderboard data\");\r\n                setLeaderboardData({ data: [], total: 0 });\r\n            }\r\n        } catch (err) {\r\n            setError(\"An unexpected error occurred\");\r\n            console.error(\"Leaderboard fetch error:\", err);\r\n            setLeaderboardData({ data: [], total: 0 });\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        fetchLeaderboardData();\r\n    }, [currentPage, appliedFilters]);\r\n\r\n    const handlePageChange = (page: number) => {\r\n        setCurrentPage(page);\r\n    };\r\n\r\n    const hasActiveFilters = Object.values(filters).some(filter => filter && filter.toString().trim() !== '');\r\n\r\n    const columns: ColumnDef<LeaderboardUser>[] = useMemo(\r\n        () => [\r\n            {\r\n                header: \"Rank\",\r\n                accessorKey: \"rank\",\r\n                cell: ({ row }) => (\r\n                    <div className=\"font-bold\">{row.original.rank}</div>\r\n                ),\r\n            },\r\n            {\r\n                header: \"Name\",\r\n                accessorKey: \"firstName\",\r\n                cell: ({ row }) => (\r\n                    <div className=\"font-semibold\">{getStudentName(row.original)}</div>\r\n                ),\r\n            },\r\n            {\r\n                header: \"Email\",\r\n                accessorKey: \"email\",\r\n                cell: ({ row }) => (\r\n                    <div>{row.original.email || '-'}</div>\r\n                ),\r\n            },\r\n            {\r\n                header: \"Score\",\r\n                accessorKey: \"score\",\r\n                cell: ({ row }) => (\r\n                    <div className=\"font-semibold\">{row.original.score.toLocaleString()}</div>\r\n                ),\r\n            },\r\n            {\r\n                header: \"Coins\",\r\n                accessorKey: \"coinEarnings\",\r\n                cell: ({ row }) => (\r\n                    <div className=\"font-semibold\">{row.original.coinEarnings.toLocaleString()}</div>\r\n                ),\r\n            },\r\n            {\r\n                header: \"Streak\",\r\n                accessorKey: \"streakCount\",\r\n                cell: ({ row }) => (\r\n                    <div>{row.original.streakCount}</div>\r\n                ),\r\n            },\r\n            {\r\n                header: \"Streak Badge\",\r\n                accessorKey: \"badge\",\r\n                cell: ({ row }) => {\r\n                    return (\r\n                        <div className=\"flex items-center \">\r\n                            <BadgeDisplay badge={row.original.badge} />\r\n                        </div>\r\n                    );\r\n                },\r\n            },\r\n            {\r\n                header: \"CoinEarnings Badge\",\r\n                accessorKey: \"coinEarnings\",\r\n                cell: ({ row }) => {\r\n                    const badgeIcon = getBadgeIcon(row.original.coinEarnings);\r\n                    const badgeAlt = getBadgeAlt(row.original.coinEarnings);\r\n\r\n                    if (badgeIcon) {\r\n                        return (\r\n                            <div className=\"flex items-center\">\r\n                                <Image\r\n                                    src={badgeIcon}\r\n                                    alt={badgeAlt}\r\n                                    width={32}\r\n                                    height={32}\r\n                                    className=\"w-8 h-8\"\r\n                                    title={badgeAlt}\r\n                                />\r\n                            </div>\r\n                        );\r\n                    }\r\n                    return null;\r\n                },\r\n            },\r\n            {\r\n                id: \"termination\",\r\n                header: \"Termination Logs\",\r\n                cell: ({ row }) => {\r\n                    return (\r\n                        <Button\r\n                            variant=\"outline\"\r\n                            size=\"sm\"\r\n                            onClick={() => fetchStudentTerminationLogs(row.original)}\r\n                            className=\"flex items-center gap-2\"\r\n                        >\r\n                            <AlertTriangle className=\"w-4 h-4\" />\r\n                            Termination\r\n                        </Button>\r\n                    );\r\n                },\r\n            },\r\n            {\r\n                header: \"Create Date\",\r\n                accessorKey: \"createdAt\",\r\n                cell: ({ row }) => {\r\n                    try {\r\n                        return (\r\n                            <div className=\"text-sm\">\r\n                                {format(new Date(row.original.createdAt), \"dd-MM-yy\")}\r\n                            </div>\r\n                        );\r\n                    } catch {\r\n                        return <div className=\"text-sm text-gray-400\">-</div>;\r\n\r\n                    }\r\n                },\r\n            },\r\n            {\r\n                header: \"Actions\",\r\n                id: \"actions\",\r\n                cell: ({ row }) => (\r\n                    <Button\r\n                        size=\"sm\"\r\n                        onClick={() => handleStudentDailyQuizeResult(\r\n                            row.original.studentId,\r\n                            row.original.firstName,\r\n                            row.original.lastName\r\n                        )}\r\n                    >\r\n                        View Results\r\n                    </Button>\r\n                ),\r\n            },\r\n\r\n        ],\r\n        []\r\n    );\r\n\r\n    const totalPages = Math.ceil(leaderboardData.total / PAGE_SIZE);\r\n\r\n    return (\r\n        <div className=\"p-6\">\r\n            <div className=\"flex justify-between items-center mb-6\">\r\n                <div>\r\n                    <h1 className=\"text-2xl font-bold\">Daily Quiz Leaderboard</h1>\r\n                    <p className=\"text-gray-600 mt-1\">All-time student performance data</p>\r\n                </div>\r\n            </div>\r\n\r\n            <div className=\"flex justify-end gap-4 mb-4\">\r\n                <Button\r\n                    variant=\"outline\"\r\n                    onClick={() => setShowFilters(!showFilters)}\r\n                    className={`flex items-center gap-2 ${hasActiveFilters ? 'border-primary text-primary' : ''}`}\r\n                    aria-label=\"Toggle filters\"\r\n                >\r\n                    <Filter className=\"w-4 h-4\" />\r\n                    Filters\r\n                </Button>\r\n            </div>\r\n\r\n            {showFilters && (\r\n                <Card className=\"mb-6\">\r\n                    <CardContent className=\"pt-6\">\r\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n                            <div>\r\n                                <Label htmlFor=\"firstName\">First Name</Label>\r\n                                <Input\r\n                                    id=\"firstName\"\r\n                                    placeholder=\"Filter by first name\"\r\n                                    value={filters.firstName || ''}\r\n                                    onChange={(e) => handleFilterChange('firstName', e.target.value)}\r\n                                />\r\n                            </div>\r\n                            <div>\r\n                                <Label htmlFor=\"lastName\">Last Name</Label>\r\n                                <Input\r\n                                    id=\"lastName\"\r\n                                    placeholder=\"Filter by last name\"\r\n                                    value={filters.lastName || ''}\r\n                                    onChange={(e) => handleFilterChange('lastName', e.target.value)}\r\n                                />\r\n                            </div>\r\n                            <div>\r\n                                <Label htmlFor=\"email\">Email</Label>\r\n                                <Input\r\n                                    id=\"email\"\r\n                                    placeholder=\"Filter by email\"\r\n                                    value={filters.email || ''}\r\n                                    onChange={(e) => handleFilterChange('email', e.target.value)}\r\n                                />\r\n                            </div>\r\n                            <div>\r\n                                <Label htmlFor=\"coins\">Coin</Label>\r\n                                <Input\r\n                                    id=\"coins\"\r\n                                    placeholder=\"Filter by coins\"\r\n                                    value={filters.coins || ''}\r\n                                    onChange={(e) => handleFilterChange('coins', e.target.value)}\r\n                                />\r\n                            </div>\r\n                            <div>\r\n                                <Label htmlFor=\"score\">Score</Label>\r\n                                <Input\r\n                                    id=\"score\"\r\n                                    type=\"number\"\r\n                                    placeholder=\"Filter by score\"\r\n                                    value={filters.score || ''}\r\n                                    onChange={(e) => handleFilterChange('score', e.target.value)}\r\n                                />\r\n                            </div>\r\n                            <div>\r\n                                <Label htmlFor=\"date\">Created Date</Label>\r\n                                <div className=\"relative\">\r\n                                    <input\r\n                                        id=\"date\"\r\n                                        type=\"date\"\r\n                                        value={filters.date || ''}\r\n                                        onChange={(e) => handleDateChange(e.target.value)}\r\n                                        className=\"w-full px-3 py-2 pl-10 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900\"\r\n                                        placeholder=\"Select date\"\r\n                                    />\r\n                                    <CalendarIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none\" />\r\n                                </div>\r\n                            </div>\r\n\r\n                        </div>\r\n\r\n                        <div className=\"flex gap-2 mt-4\">\r\n                            <Button\r\n                                onClick={handleSearch}\r\n                                disabled={loading}\r\n                            >\r\n                                Search\r\n                            </Button>\r\n                            <Button\r\n                                variant=\"outline\"\r\n                                onClick={handleResetFilters}\r\n                                disabled={loading}\r\n                            >\r\n                                Reset\r\n                            </Button>\r\n                        </div>\r\n                    </CardContent>\r\n                </Card>\r\n            )}\r\n\r\n            {error && (\r\n                <div className=\"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg\">\r\n                    <p className=\"text-red-600 font-medium\">{error}</p>\r\n                </div>\r\n            )}\r\n\r\n            <DataTable\r\n                columns={columns}\r\n                data={leaderboardData.data}\r\n                isLoading={loading}\r\n            />\r\n\r\n            {leaderboardData.total > 0 && (\r\n                <div className=\"mt-6\">\r\n                    <Pagination\r\n                        page={currentPage}\r\n                        totalPages={totalPages}\r\n                        setPage={handlePageChange}\r\n                        entriesText={`${leaderboardData.data.length} of ${leaderboardData.total} entries`}\r\n                    />\r\n                </div>\r\n            )}\r\n\r\n            {leaderboardData.data.length === 0 && !loading && !error && (\r\n                <div className=\"text-center py-12 bg-white rounded-lg shadow\">\r\n                    <div className=\"text-gray-400 text-6xl mb-4\">📊</div>\r\n                    <p className=\"text-gray-500 text-lg\">No leaderboard data available</p>\r\n                    <p className=\"text-gray-400 text-sm mt-2\">Check back later for updated data.</p>\r\n                </div>\r\n            )}\r\n\r\n            {/* Termination Logs Modal */}\r\n            {selectedStudentForTermination && (\r\n                <div className=\"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4\">\r\n                    <div className=\"bg-white rounded-lg max-w-4xl max-h-[90vh] overflow-auto w-full\">\r\n                        <div className=\"p-4 border-b sticky top-0 bg-white\">\r\n                            <div className=\"flex justify-between items-center\">\r\n                                <div>\r\n                                    <h3 className=\"font-semibold text-lg\">\r\n                                        Termination Logs - {selectedStudentForTermination.firstName} {selectedStudentForTermination.lastName}\r\n                                    </h3>\r\n                                    <p className=\"text-sm text-gray-500\">\r\n                                        Student ID: {selectedStudentForTermination.studentId} | Email: {selectedStudentForTermination.email} | Rank: {selectedStudentForTermination.rank}\r\n                                    </p>\r\n                                </div>\r\n                                <div className=\"flex gap-2\">\r\n                                    <Button\r\n                                        variant=\"outline\"\r\n                                        onClick={() => fetchStudentTerminationLogs(selectedStudentForTermination)}\r\n                                        disabled={terminationLogsLoading}\r\n                                    >\r\n                                        <RefreshCw className={`w-4 h-4 mr-2 ${terminationLogsLoading ? \"animate-spin\" : \"\"}`} />\r\n                                        Refresh\r\n                                    </Button>\r\n                                    <Button\r\n                                        variant=\"outline\"\r\n                                        onClick={() => {\r\n                                            setSelectedStudentForTermination(null);\r\n                                            setTerminationLogs([]);\r\n                                        }}\r\n                                    >\r\n                                        <X className=\"w-4 h-4\" />\r\n                                    </Button>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div className=\"p-4\">\r\n                            {terminationLogsLoading ? (\r\n                                <div className=\"flex justify-center items-center py-12\">\r\n                                    <RefreshCw className=\"w-8 h-8 animate-spin text-primary\" />\r\n                                </div>\r\n                            ) : terminationLogs.length > 0 ? (\r\n                                <div className=\"space-y-4\">\r\n                                    <p className=\"text-sm text-gray-600\">\r\n                                        Found {terminationLogs.length} termination logs for this student\r\n                                    </p>\r\n                                    <div className=\"space-y-3\">\r\n                                        {terminationLogs.map((log, index) => (\r\n                                            <div\r\n                                                key={log.id}\r\n                                                className=\"border rounded-lg p-4 bg-red-50 border-red-200\"\r\n                                            >\r\n                                                <div className=\"flex justify-between items-start\">\r\n                                                    <div className=\"flex-1\">\r\n                                                        <div className=\"flex items-center gap-2 mb-2\">\r\n                                                            <span className=\"bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded\">\r\n                                                                Termination #{index + 1}\r\n                                                            </span>\r\n                                                            <span className=\"text-sm text-gray-500\">\r\n                                                                {new Date(log.createdAt).toLocaleString()}\r\n                                                            </span>\r\n                                                        </div>\r\n                                                        <div className=\"text-sm\">\r\n                                                            <strong>Reason:</strong> {log.reason}\r\n                                                        </div>\r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        ))}\r\n                                    </div>\r\n                                </div>\r\n                            ) : (\r\n                                <div className=\"text-center py-12\">\r\n                                    <X className=\"w-12 h-12 mx-auto text-gray-400 mb-4\" />\r\n                                    <p className=\"text-gray-500\">\r\n                                        No termination logs found for this student\r\n                                    </p>\r\n                                </div>\r\n                            )}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            )}\r\n        </div>\r\n    );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAhBA;;;;;;;;;;;;;;;;AAsDA,MAAM,YAAY;AAEH,SAAS;IACpB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;QACpE,MAAM,EAAE;QACR,OAAO;IACX;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B;QAC/D,WAAW;QACX,UAAU;QACV,OAAO;QACP,OAAO;QACP,OAAO;QACP,MAAM;IAEV;IAEA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B,CAAC;IAClF,MAAM,CAAC,+BAA+B,iCAAiC,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IAC3G,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC3E,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErE,MAAM,YAAY;IAElB,MAAM,eAAe;QACjB,QAAQ,GAAG,CAAC,qBAAqB;QACjC,eAAe;QACf,kBAAkB;YAAE,GAAG,OAAO;QAAC;IACnC;IACA,MAAM,gCAAgC,CAAC,WAAmB,WAA0B;QAChF,MAAM,iBAAiB,aAAa;QACpC,MAAM,gBAAgB,YAAY;QAClC,OAAO,IAAI,CAAC,CAAC,2BAA2B,EAAE,UAAU,WAAW,EAAE,mBAAmB,gBAAgB,UAAU,EAAE,mBAAmB,gBAAgB;IACvJ;IACA,MAAM,qBAAqB;QACvB,QAAQ,GAAG,CAAC;QACZ,MAAM,eAA2C;YAC7C,WAAW;YACX,UAAU;YACV,OAAO;YACP,OAAO;YACP,OAAO;YACP,MAAM;QAEV;QACA,WAAW;QACX,kBAAkB,CAAC;QACnB,eAAe;IACnB;IAEA,MAAM,qBAAqB,CAAC,OAAyC;QACjE,WAAW,CAAC,OAAS,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACb,CAAC;IACL;IACA,MAAM,mBAAmB,CAAC;QACtB,WAAW,CAAC,OAAS,CAAC;gBAClB,GAAG,IAAI;gBACP,MAAM;YACV,CAAC;IACL;IACA,MAAM,eAAe,CAAC;QAClB,IAAI,gBAAgB,OAAO,gBAAgB,KAAK;YAC5C,OAAO;QACX,OAAO,IAAI,gBAAgB,OAAO,gBAAgB,KAAK;YACnD,OAAO;QACX,OAAO,IAAI,gBAAgB,MAAM;YAC7B,OAAO;QACX;QACA,OAAO;IACX;IAEA,MAAM,cAAc,CAAC;QACjB,IAAI,gBAAgB,OAAO,gBAAgB,KAAK;YAC5C,OAAO;QACX,OAAO,IAAI,gBAAgB,OAAO,gBAAgB,KAAK;YACnD,OAAO;QACX,OAAO,IAAI,gBAAgB,MAAM;YAC7B,OAAO;QACX;QACA,OAAO;IACX;IAEA,MAAM,iBAAiB,CAAC;QACpB,IAAI,KAAK,SAAS,IAAI,KAAK,QAAQ,EAAE;YACjC,OAAO,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;QAC/C;QACA,OAAO,KAAK,KAAK,IAAI,CAAC,QAAQ,EAAE,KAAK,SAAS,EAAE;IACpD;IAEA,MAAM,8BAA8B,OAAO;QACvC,iCAAiC;QACjC,0BAA0B;QAC1B,mBAAmB,EAAE;QAErB,IAAI;YACA,MAAM,WAAW,MAAM,CAAA,GAAA,6IAAA,CAAA,2BAAwB,AAAD,EAAE,QAAQ,SAAS;YAEjE,IAAI,SAAS,OAAO,EAAE;gBAClB,mBAAmB,SAAS,IAAI,IAAI,EAAE;YAC1C,OAAO;gBACH,QAAQ,KAAK,CAAC,qCAAqC,SAAS,KAAK;gBACjE,mBAAmB,EAAE;YACzB;QACJ,EAAE,OAAO,OAAO;YACZ,QAAQ,KAAK,CAAC,oCAAoC;YAClD,mBAAmB,EAAE;QACzB,SAAU;YACN,0BAA0B;QAC9B;IACJ;IAEA,MAAM,uBAAuB;QACzB,WAAW;QACX,SAAS;QAET,IAAI;YACA,MAAM,WAAW,MAAM,CAAA,GAAA,gIAAA,CAAA,yBAAsB,AAAD,EAAE,WAAW,aAAa,WAAW;YAEjF,IAAI,SAAS,OAAO,EAAE;gBAClB,mBAAmB,SAAS,IAAI;YACpC,OAAO;gBACH,SAAS,SAAS,KAAK,IAAI;gBAC3B,mBAAmB;oBAAE,MAAM,EAAE;oBAAE,OAAO;gBAAE;YAC5C;QACJ,EAAE,OAAO,KAAK;YACV,SAAS;YACT,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,mBAAmB;gBAAE,MAAM,EAAE;gBAAE,OAAO;YAAE;QAC5C,SAAU;YACN,WAAW;QACf;IACJ;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN;IACJ,GAAG;QAAC;QAAa;KAAe;IAEhC,MAAM,mBAAmB,CAAC;QACtB,eAAe;IACnB;IAEA,MAAM,mBAAmB,OAAO,MAAM,CAAC,SAAS,IAAI,CAAC,CAAA,SAAU,UAAU,OAAO,QAAQ,GAAG,IAAI,OAAO;IAEtG,MAAM,UAAwC,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAChD,IAAM;YACF;gBACI,QAAQ;gBACR,aAAa;gBACb,MAAM,CAAC,EAAE,GAAG,EAAE,iBACV,8OAAC;wBAAI,WAAU;kCAAa,IAAI,QAAQ,CAAC,IAAI;;;;;;YAErD;YACA;gBACI,QAAQ;gBACR,aAAa;gBACb,MAAM,CAAC,EAAE,GAAG,EAAE,iBACV,8OAAC;wBAAI,WAAU;kCAAiB,eAAe,IAAI,QAAQ;;;;;;YAEnE;YACA;gBACI,QAAQ;gBACR,aAAa;gBACb,MAAM,CAAC,EAAE,GAAG,EAAE,iBACV,8OAAC;kCAAK,IAAI,QAAQ,CAAC,KAAK,IAAI;;;;;;YAEpC;YACA;gBACI,QAAQ;gBACR,aAAa;gBACb,MAAM,CAAC,EAAE,GAAG,EAAE,iBACV,8OAAC;wBAAI,WAAU;kCAAiB,IAAI,QAAQ,CAAC,KAAK,CAAC,cAAc;;;;;;YAEzE;YACA;gBACI,QAAQ;gBACR,aAAa;gBACb,MAAM,CAAC,EAAE,GAAG,EAAE,iBACV,8OAAC;wBAAI,WAAU;kCAAiB,IAAI,QAAQ,CAAC,YAAY,CAAC,cAAc;;;;;;YAEhF;YACA;gBACI,QAAQ;gBACR,aAAa;gBACb,MAAM,CAAC,EAAE,GAAG,EAAE,iBACV,8OAAC;kCAAK,IAAI,QAAQ,CAAC,WAAW;;;;;;YAEtC;YACA;gBACI,QAAQ;gBACR,aAAa;gBACb,MAAM,CAAC,EAAE,GAAG,EAAE;oBACV,qBACI,8OAAC;wBAAI,WAAU;kCACX,cAAA,8OAAC,wIAAA,CAAA,UAAY;4BAAC,OAAO,IAAI,QAAQ,CAAC,KAAK;;;;;;;;;;;gBAGnD;YACJ;YACA;gBACI,QAAQ;gBACR,aAAa;gBACb,MAAM,CAAC,EAAE,GAAG,EAAE;oBACV,MAAM,YAAY,aAAa,IAAI,QAAQ,CAAC,YAAY;oBACxD,MAAM,WAAW,YAAY,IAAI,QAAQ,CAAC,YAAY;oBAEtD,IAAI,WAAW;wBACX,qBACI,8OAAC;4BAAI,WAAU;sCACX,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACF,KAAK;gCACL,KAAK;gCACL,OAAO;gCACP,QAAQ;gCACR,WAAU;gCACV,OAAO;;;;;;;;;;;oBAIvB;oBACA,OAAO;gBACX;YACJ;YACA;gBACI,IAAI;gBACJ,QAAQ;gBACR,MAAM,CAAC,EAAE,GAAG,EAAE;oBACV,qBACI,8OAAC,kIAAA,CAAA,SAAM;wBACH,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,4BAA4B,IAAI,QAAQ;wBACvD,WAAU;;0CAEV,8OAAC,wNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;4BAAY;;;;;;;gBAIjD;YACJ;YACA;gBACI,QAAQ;gBACR,aAAa;gBACb,MAAM,CAAC,EAAE,GAAG,EAAE;oBACV,IAAI;wBACA,qBACI,8OAAC;4BAAI,WAAU;sCACV,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,IAAI,QAAQ,CAAC,SAAS,GAAG;;;;;;oBAGtD,EAAE,OAAM;wBACJ,qBAAO,8OAAC;4BAAI,WAAU;sCAAwB;;;;;;oBAElD;gBACJ;YACJ;YACA;gBACI,QAAQ;gBACR,IAAI;gBACJ,MAAM,CAAC,EAAE,GAAG,EAAE,iBACV,8OAAC,kIAAA,CAAA,SAAM;wBACH,MAAK;wBACL,SAAS,IAAM,8BACX,IAAI,QAAQ,CAAC,SAAS,EACtB,IAAI,QAAQ,CAAC,SAAS,EACtB,IAAI,QAAQ,CAAC,QAAQ;kCAE5B;;;;;;YAIT;SAEH,EACD,EAAE;IAGN,MAAM,aAAa,KAAK,IAAI,CAAC,gBAAgB,KAAK,GAAG;IAErD,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC;gBAAI,WAAU;0BACX,cAAA,8OAAC;;sCACG,8OAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;0BAI1C,8OAAC;gBAAI,WAAU;0BACX,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACH,SAAQ;oBACR,SAAS,IAAM,eAAe,CAAC;oBAC/B,WAAW,CAAC,wBAAwB,EAAE,mBAAmB,gCAAgC,IAAI;oBAC7F,cAAW;;sCAEX,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBAAY;;;;;;;;;;;;YAKrC,6BACG,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACZ,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACnB,8OAAC;4BAAI,WAAU;;8CACX,8OAAC;;sDACG,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAY;;;;;;sDAC3B,8OAAC,iIAAA,CAAA,QAAK;4CACF,IAAG;4CACH,aAAY;4CACZ,OAAO,QAAQ,SAAS,IAAI;4CAC5B,UAAU,CAAC,IAAM,mBAAmB,aAAa,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAGvE,8OAAC;;sDACG,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAW;;;;;;sDAC1B,8OAAC,iIAAA,CAAA,QAAK;4CACF,IAAG;4CACH,aAAY;4CACZ,OAAO,QAAQ,QAAQ,IAAI;4CAC3B,UAAU,CAAC,IAAM,mBAAmB,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAGtE,8OAAC;;sDACG,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,8OAAC,iIAAA,CAAA,QAAK;4CACF,IAAG;4CACH,aAAY;4CACZ,OAAO,QAAQ,KAAK,IAAI;4CACxB,UAAU,CAAC,IAAM,mBAAmB,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAGnE,8OAAC;;sDACG,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,8OAAC,iIAAA,CAAA,QAAK;4CACF,IAAG;4CACH,aAAY;4CACZ,OAAO,QAAQ,KAAK,IAAI;4CACxB,UAAU,CAAC,IAAM,mBAAmB,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAGnE,8OAAC;;sDACG,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,8OAAC,iIAAA,CAAA,QAAK;4CACF,IAAG;4CACH,MAAK;4CACL,aAAY;4CACZ,OAAO,QAAQ,KAAK,IAAI;4CACxB,UAAU,CAAC,IAAM,mBAAmB,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAGnE,8OAAC;;sDACG,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAO;;;;;;sDACtB,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDACG,IAAG;oDACH,MAAK;oDACL,OAAO,QAAQ,IAAI,IAAI;oDACvB,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDAChD,WAAU;oDACV,aAAY;;;;;;8DAEhB,8OAAC,8MAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAMpC,8OAAC;4BAAI,WAAU;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCACH,SAAS;oCACT,UAAU;8CACb;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACH,SAAQ;oCACR,SAAS;oCACT,UAAU;8CACb;;;;;;;;;;;;;;;;;;;;;;;YAQhB,uBACG,8OAAC;gBAAI,WAAU;0BACX,cAAA,8OAAC;oBAAE,WAAU;8BAA4B;;;;;;;;;;;0BAIjD,8OAAC,sIAAA,CAAA,YAAS;gBACN,SAAS;gBACT,MAAM,gBAAgB,IAAI;gBAC1B,WAAW;;;;;;YAGd,gBAAgB,KAAK,GAAG,mBACrB,8OAAC;gBAAI,WAAU;0BACX,cAAA,8OAAC,uIAAA,CAAA,UAAU;oBACP,MAAM;oBACN,YAAY;oBACZ,SAAS;oBACT,aAAa,GAAG,gBAAgB,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,gBAAgB,KAAK,CAAC,QAAQ,CAAC;;;;;;;;;;;YAK5F,gBAAgB,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,WAAW,CAAC,uBAC/C,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;wBAAI,WAAU;kCAA8B;;;;;;kCAC7C,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;kCACrC,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;YAKjD,+CACG,8OAAC;gBAAI,WAAU;0BACX,cAAA,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAI,WAAU;sCACX,cAAA,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;;0DACG,8OAAC;gDAAG,WAAU;;oDAAwB;oDACd,8BAA8B,SAAS;oDAAC;oDAAE,8BAA8B,QAAQ;;;;;;;0DAExG,8OAAC;gDAAE,WAAU;;oDAAwB;oDACpB,8BAA8B,SAAS;oDAAC;oDAAW,8BAA8B,KAAK;oDAAC;oDAAU,8BAA8B,IAAI;;;;;;;;;;;;;kDAGxJ,8OAAC;wCAAI,WAAU;;0DACX,8OAAC,kIAAA,CAAA,SAAM;gDACH,SAAQ;gDACR,SAAS,IAAM,4BAA4B;gDAC3C,UAAU;;kEAEV,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAW,CAAC,aAAa,EAAE,yBAAyB,iBAAiB,IAAI;;;;;;oDAAI;;;;;;;0DAG5F,8OAAC,kIAAA,CAAA,SAAM;gDACH,SAAQ;gDACR,SAAS;oDACL,iCAAiC;oDACjC,mBAAmB,EAAE;gDACzB;0DAEA,cAAA,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAK7B,8OAAC;4BAAI,WAAU;sCACV,uCACG,8OAAC;gCAAI,WAAU;0CACX,cAAA,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;uCAEzB,gBAAgB,MAAM,GAAG,kBACzB,8OAAC;gCAAI,WAAU;;kDACX,8OAAC;wCAAE,WAAU;;4CAAwB;4CAC1B,gBAAgB,MAAM;4CAAC;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;kDACV,gBAAgB,GAAG,CAAC,CAAC,KAAK,sBACvB,8OAAC;gDAEG,WAAU;0DAEV,cAAA,8OAAC;oDAAI,WAAU;8DACX,cAAA,8OAAC;wDAAI,WAAU;;0EACX,8OAAC;gEAAI,WAAU;;kFACX,8OAAC;wEAAK,WAAU;;4EAAoE;4EAClE,QAAQ;;;;;;;kFAE1B,8OAAC;wEAAK,WAAU;kFACX,IAAI,KAAK,IAAI,SAAS,EAAE,cAAc;;;;;;;;;;;;0EAG/C,8OAAC;gEAAI,WAAU;;kFACX,8OAAC;kFAAO;;;;;;oEAAgB;oEAAE,IAAI,MAAM;;;;;;;;;;;;;;;;;;+CAd3C,IAAI,EAAE;;;;;;;;;;;;;;;qDAuB3B,8OAAC;gCAAI,WAAU;;kDACX,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;kDACb,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWjE", "debugId": null}}]}