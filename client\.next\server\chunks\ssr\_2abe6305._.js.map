{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx';\r\nimport { twMerge } from 'tailwind-merge';\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport const truncateThought = (text: string, wordLimit: number = 5): string => {\r\n  const words = text.trim().split(/\\s+/);\r\n  if (words.length <= wordLimit) return text;\r\n  return words.slice(0, wordLimit).join(' ') + '...';\r\n};\r\n\r\nexport const setStudentAuthToken = (token: string) => {\r\n  localStorage.setItem('studentToken', token);\r\n};\r\n\r\nexport const getStudentAuthToken = (): string | null => {\r\n  if (typeof window !== 'undefined') {\r\n    return localStorage.getItem('studentToken');\r\n  }\r\n  return null;\r\n};\r\n\r\nexport const clearStudentAuthToken = () => {\r\n  localStorage.removeItem('studentToken');\r\n};\r\n\r\nexport const isStudentAuthenticated = (): boolean => {\r\n  return !!getStudentAuthToken();\r\n};\r\n\r\nexport const isAuthenticated = (): { isAuth: boolean; userType: 'STUDENT' | 'CLASS' | null } => {\r\n  const studentToken = getStudentAuthToken();\r\n  if (studentToken) {\r\n    return { isAuth: true, userType: 'STUDENT' };\r\n  }\r\n\r\n  if (typeof window !== 'undefined') {\r\n    const userData = localStorage.getItem('user');\r\n    if (userData) {\r\n      try {\r\n        const user = JSON.parse(userData);\r\n        if (user && user.id) {\r\n          return { isAuth: true, userType: 'CLASS' };\r\n        }\r\n      } catch (error) {\r\n        console.error('Error parsing user data:', error);\r\n      }\r\n    }\r\n  }\r\n\r\n  return { isAuth: false, userType: null };\r\n};"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,kBAAkB,CAAC,MAAc,YAAoB,CAAC;IACjE,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC;IAChC,IAAI,MAAM,MAAM,IAAI,WAAW,OAAO;IACtC,OAAO,MAAM,KAAK,CAAC,GAAG,WAAW,IAAI,CAAC,OAAO;AAC/C;AAEO,MAAM,sBAAsB,CAAC;IAClC,aAAa,OAAO,CAAC,gBAAgB;AACvC;AAEO,MAAM,sBAAsB;IACjC,uCAAmC;;IAEnC;IACA,OAAO;AACT;AAEO,MAAM,wBAAwB;IACnC,aAAa,UAAU,CAAC;AAC1B;AAEO,MAAM,yBAAyB;IACpC,OAAO,CAAC,CAAC;AACX;AAEO,MAAM,kBAAkB;IAC7B,MAAM,eAAe;IACrB,IAAI,cAAc;QAChB,OAAO;YAAE,QAAQ;YAAM,UAAU;QAAU;IAC7C;IAEA,uCAAmC;;IAYnC;IAEA,OAAO;QAAE,QAAQ;QAAO,UAAU;IAAK;AACzC", "debugId": null}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\r\n        destructive:\r\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline:\r\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\r\n        secondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\r\n        ghost: 'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\r\n        link: 'text-primary underline-offset-4 hover:underline',\r\n      },\r\n      size: {\r\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\r\n        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\r\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\r\n        icon: 'size-9',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default',\r\n    },\r\n  }\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : 'button';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/store/hooks.ts"], "sourcesContent": ["import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';\r\nimport type { RootState, AppDispatch } from './index';\r\n\r\n// Use throughout your app instead of plain `useDispatch` and `useSelector`\r\nexport const useAppDispatch = () => useDispatch<AppDispatch>();\r\nexport const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAIO,MAAM,iBAAiB,IAAM,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;AACvC,MAAM,iBAAkD,yJAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/ProfileCompletionIndicator.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { Button } from '@/components/ui/button';\r\nimport { User, ArrowRight } from 'lucide-react';\r\nimport { useSelector } from 'react-redux';\r\nimport { RootState } from '@/store';\r\n\r\nconst ProfileCompletionIndicator = () => {\r\n  const router = useRouter();\r\n  const { profileData } = useSelector((state: RootState) => state.studentProfile);\r\n\r\n  // Check if student is logged in\r\n  const isLoggedIn = typeof window !== 'undefined' && localStorage.getItem('studentToken') !== null;\r\n\r\n  // Check if student has any profile data - only check if profile ID exists\r\n  const hasProfile = profileData?.profile?.id !== undefined;\r\n\r\n  // Only show the indicator if student is logged in and doesn't have a profile\r\n  if (!isLoggedIn || hasProfile) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className=\"my-4 mx-10 sm:px-4\">\r\n      <div className=\"bg-white dark:bg-gray-900 border border-orange-200 overflow-hidden dark:border-orange-900\">\r\n        <div className=\"px-3 py-1.5 flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <div className=\"bg-[#ff914d] p-1.5 rounded-full\">\r\n            <User className=\"h-3 w-3 text-white\" />\r\n          </div>\r\n          <p className=\"text-xs font-medium text-gray-800 dark:text-gray-200\">\r\n            Please Complete your profile \r\n          </p>\r\n        </div>\r\n        <Button\r\n          onClick={() => router.push('/student/profile')}\r\n          className=\"bg-[#ff914d] hover:bg-[#e07c3a] text-white text-xs px-2 py-0.5 h-6 min-w-0\"\r\n          size=\"sm\"\r\n        >\r\n         Complete now <ArrowRight className=\"h-3 w-3\" />\r\n        </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProfileCompletionIndicator;\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;;AASA,MAAM,6BAA6B;IACjC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAqB,MAAM,cAAc;IAE9E,gCAAgC;IAChC,MAAM,aAAa,gBAAkB,eAAe,aAAa,OAAO,CAAC,oBAAoB;IAE7F,0EAA0E;IAC1E,MAAM,aAAa,aAAa,SAAS,OAAO;IAEhD,6EAA6E;IAC7E,wCAA+B;QAC7B,OAAO;IACT;;AAyBF;uCAEe", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/popover.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as PopoverPrimitive from '@radix-ui/react-popover';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Popover({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />;\r\n}\r\n\r\nfunction PopoverTrigger({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />;\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = 'center',\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot=\"popover-content\"\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden',\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction PopoverAnchor({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />;\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor };\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EAAE,GAAG,OAA2D;IAC/E,qBAAO,8OAAC,mKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;AAEA,SAAS,eAAe,EAAE,GAAG,OAA8D;IACzF,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,cAAc,EAAE,GAAG,OAA6D;IACvF,qBAAO,8OAAC,mKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction AlertDialog({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Root>) {\r\n  return <AlertDialogPrimitive.Root data-slot=\"alert-dialog\" {...props} />\r\n}\r\n\r\nfunction AlertDialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Trigger>) {\r\n  return (\r\n    <AlertDialogPrimitive.Trigger data-slot=\"alert-dialog-trigger\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Portal>) {\r\n  return (\r\n    <AlertDialogPrimitive.Portal data-slot=\"alert-dialog-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Overlay>) {\r\n  return (\r\n    <AlertDialogPrimitive.Overlay\r\n      data-slot=\"alert-dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Content>) {\r\n  return (\r\n    <AlertDialogPortal>\r\n      <AlertDialogOverlay />\r\n      <AlertDialogPrimitive.Content\r\n        data-slot=\"alert-dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </AlertDialogPortal>\r\n  )\r\n}\r\n\r\nfunction AlertDialogHeader({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogFooter({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Title>) {\r\n  return (\r\n    <AlertDialogPrimitive.Title\r\n      data-slot=\"alert-dialog-title\"\r\n      className={cn(\"text-lg font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Description>) {\r\n  return (\r\n    <AlertDialogPrimitive.Description\r\n      data-slot=\"alert-dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogAction({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Action>) {\r\n  return (\r\n    <AlertDialogPrimitive.Action\r\n      className={cn(buttonVariants(), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogCancel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Cancel>) {\r\n  return (\r\n    <AlertDialogPrimitive.Cancel\r\n      className={cn(buttonVariants({ variant: \"outline\" }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGA;AAEA;AACA;AANA;;;;;AAQA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,8OAAC,2KAAA,CAAA,OAAyB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,2KAAA,CAAA,UAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,2KAAA,CAAA,SAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC,2KAAA,CAAA,UAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,2KAAA,CAAA,UAA4B;gBAC3B,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,2KAAA,CAAA,QAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,2KAAA,CAAA,cAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IAAI;QACrD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 398, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/notificationService.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\n\r\nexport interface Notification {\r\n  id: string;\r\n  userId: string;\r\n  userType: 'STUDENT' | 'CLASS' | 'ADMIN';\r\n  type: 'STUDENT_ACCOUNT_CREATED' | 'STUDENT_PROFILE_APPROVED' | 'STUDENT_PROFILE_REJECTED' |\r\n        'STUDENT_COIN_PURCHASE' | 'STUDENT_UWHIZ_PARTICIPATION' | 'STUDENT_CHAT_MESSAGE' |\r\n        'STUDENT_STORE_PURCHASE' | 'STUDENT_STORE_ORDER_APPROVED' | 'STUDENT_STORE_ORDER_REJECTED' |\r\n        'CLASS_ACCOUNT_CREATED' | 'CLASS_PROFILE_APPROVED' | 'CLASS_PROFILE_REJECTED' |\r\n        'CLASS_COIN_PURCHASE' | 'CLASS_CHAT_MESSAGE' | 'CLASS_CONTENT_APPROVED' | 'CLASS_CONTENT_REJECTED' |\r\n        'CLASS_EDUCATION_ADDED' | 'CLASS_EXPERIENCE_ADDED' | 'CLASS_CERTIFICATE_ADDED' |\r\n        'CLASS_STORE_PURCHASE' | 'CLASS_STORE_ORDER_APPROVED' | 'CLASS_STORE_ORDER_REJECTED' |\r\n        'ADMIN_NEW_STUDENT_REGISTRATION' | 'ADMIN_NEW_CLASS_REGISTRATION' |\r\n        'ADMIN_PROFILE_REVIEW_REQUIRED' | 'ADMIN_CONTENT_REVIEW_REQUIRED' | 'ADMIN_NEW_STORE_ORDER';\r\n  title: string;\r\n  message: string;\r\n  data?: any;\r\n  isRead: boolean;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\nexport interface NotificationPagination {\r\n  currentPage: number;\r\n  totalPages: number;\r\n  totalCount: number;\r\n  limit: number;\r\n  hasNextPage: boolean;\r\n  hasPrevPage: boolean;\r\n}\r\n\r\nexport interface NotificationResponse {\r\n  notifications: Notification[];\r\n  pagination: NotificationPagination;\r\n}\r\n\r\n// For Classes (authenticated users)\r\nexport const getClassNotifications = async (page: number = 1, limit: number = 10): Promise<NotificationResponse> => {\r\n  const response = await axiosInstance.get(`/notifications/classes?page=${page}&limit=${limit}`);\r\n  return response.data.data;\r\n};\r\n\r\n\r\n\r\nexport const getClassUnreadCount = async (): Promise<number> => {\r\n  const response = await axiosInstance.get('/notifications/classes/count');\r\n  return response.data.data.count;\r\n};\r\n\r\nexport const markClassNotificationAsRead = async (notificationId: string) => {\r\n  const response = await axiosInstance.post(`/notifications/classes/mark-read/${notificationId}`);\r\n  return response.data;\r\n};\r\n\r\nexport const markAllClassNotificationsAsRead = async () => {\r\n  const response = await axiosInstance.post('/notifications/classes/mark-all-read');\r\n  return response.data;\r\n};\r\n\r\nexport const deleteAllClassNotifications = async () => {\r\n  const response = await axiosInstance.delete('/notifications/classes/delete-all');\r\n  return response.data;\r\n};\r\n\r\n// For Students (bearer token auth)\r\nexport const getStudentNotifications = async (page: number = 1, limit: number = 10): Promise<NotificationResponse> => {\r\n  const response = await axiosInstance.get(`/notifications/students?page=${page}&limit=${limit}`);\r\n  return response.data.data;\r\n};\r\n\r\n\r\n\r\nexport const getStudentUnreadCount = async (): Promise<number> => {\r\n  const response = await axiosInstance.get('/notifications/students/count');\r\n  return response.data.data.count;\r\n};\r\n\r\nexport const markStudentNotificationAsRead = async (notificationId: string) => {\r\n  const response = await axiosInstance.post(`/notifications/students/mark-read/${notificationId}`);\r\n  return response.data;\r\n};\r\n\r\nexport const markAllStudentNotificationsAsRead = async () => {\r\n  const response = await axiosInstance.post('/notifications/students/mark-all-read');\r\n  return response.data;\r\n};\r\n\r\nexport const deleteAllStudentNotifications = async () => {\r\n  const response = await axiosInstance.delete('/notifications/students/delete-all');\r\n  return response.data;\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AAsCO,MAAM,wBAAwB,OAAO,OAAe,CAAC,EAAE,QAAgB,EAAE;IAC9E,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,4BAA4B,EAAE,KAAK,OAAO,EAAE,OAAO;IAC7F,OAAO,SAAS,IAAI,CAAC,IAAI;AAC3B;AAIO,MAAM,sBAAsB;IACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;IACzC,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK;AACjC;AAEO,MAAM,8BAA8B,OAAO;IAChD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,iCAAiC,EAAE,gBAAgB;IAC9F,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,kCAAkC;IAC7C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC;IAC1C,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,8BAA8B;IACzC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;IAC5C,OAAO,SAAS,IAAI;AACtB;AAGO,MAAM,0BAA0B,OAAO,OAAe,CAAC,EAAE,QAAgB,EAAE;IAChF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,KAAK,OAAO,EAAE,OAAO;IAC9F,OAAO,SAAS,IAAI,CAAC,IAAI;AAC3B;AAIO,MAAM,wBAAwB;IACnC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;IACzC,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK;AACjC;AAEO,MAAM,gCAAgC,OAAO;IAClD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,kCAAkC,EAAE,gBAAgB;IAC/F,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,oCAAoC;IAC/C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC;IAC1C,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,gCAAgC;IAC3C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;IAC5C,OAAO,SAAS,IAAI;AACtB", "debugId": null}}, {"offset": {"line": 458, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/NotificationBell.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { Bell } from 'lucide-react';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from '@/components/ui/popover';\r\n\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog';\r\nimport {\r\n  getClassNotifications,\r\n  getClassUnreadCount,\r\n  markClassNotificationAsRead,\r\n  markAllClassNotificationsAsRead,\r\n  deleteAllClassNotifications,\r\n  getStudentNotifications,\r\n  getStudentUnreadCount,\r\n  markStudentNotificationAsRead,\r\n  markAllStudentNotificationsAsRead,\r\n  deleteAllStudentNotifications,\r\n  Notification\r\n} from '@/services/notificationService';\r\nimport { toast } from 'sonner';\r\nimport { formatDistanceToNow } from 'date-fns';\r\nimport { useRouter } from 'next/navigation';\r\n\r\ninterface NotificationBellProps {\r\n  userType: 'class' | 'student';\r\n}\r\n\r\nexport default function NotificationBell({ userType }: NotificationBellProps) {\r\n  const [notifications, setNotifications] = useState<Notification[]>([]);\r\n  const [unreadCount, setUnreadCount] = useState(0);\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [loading, setLoading] = useState(false);\r\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\r\n\r\n  const router = useRouter();\r\n\r\n  const safeNotifications = Array.isArray(notifications) ? notifications : [];\r\n\r\n  const fetchNotifications = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      let result: any;\r\n      let count: number;\r\n\r\n      if (userType === 'class') {\r\n        result = await getClassNotifications(1, 20);\r\n        count = await getClassUnreadCount();\r\n      } else {\r\n        result = await getStudentNotifications(1, 20);\r\n        count = await getStudentUnreadCount();\r\n      }\r\n\r\n      // Handle both old and new response formats\r\n      const notifs = result?.notifications || result || [];\r\n      setNotifications(Array.isArray(notifs) ? notifs : []);\r\n      setUnreadCount(count);\r\n    } catch (error) {\r\n      console.error('Error fetching notifications:', error);\r\n      setNotifications([]);\r\n      setUnreadCount(0);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [userType]);\r\n\r\n  const handleNotificationClick = async (notification: Notification) => {\r\n    try {\r\n      // Mark notification as read\r\n      if (userType === 'class') {\r\n        await markClassNotificationAsRead(notification.id);\r\n      } else {\r\n        await markStudentNotificationAsRead(notification.id);\r\n      }\r\n\r\n      // Update local state\r\n      setNotifications(prev =>\r\n        prev.map(notif =>\r\n          notif.id === notification.id ? { ...notif, isRead: true } : notif\r\n        )\r\n      );\r\n      setUnreadCount(prev => Math.max(0, prev - 1));\r\n      setIsOpen(false);\r\n      if (notification.data?.actionType === 'OPEN_CHAT' && notification.data?.redirectUrl) {\r\n        router.push(notification.data.redirectUrl);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error handling notification click:', error);\r\n      toast.error('Failed to process notification');\r\n    }\r\n  };\r\n\r\n  const handleMarkAllAsRead = async () => {\r\n    try {\r\n      if (userType === 'class') {\r\n        await markAllClassNotificationsAsRead();\r\n      } else {\r\n        await markAllStudentNotificationsAsRead();\r\n      }\r\n\r\n      // Update local state\r\n      setNotifications(prev =>\r\n        prev.map(notif => ({ ...notif, isRead: true }))\r\n      );\r\n      setUnreadCount(0);\r\n      toast.success('All notifications marked as read');\r\n    } catch (error) {\r\n      console.error('Error marking all notifications as read:', error);\r\n      toast.error('Failed to mark all notifications as read');\r\n    }\r\n  };\r\n\r\n  const handleRemoveAllClick = () => {\r\n    setShowDeleteDialog(true);\r\n  };\r\n\r\n  const handleConfirmRemoveAll = async () => {\r\n    setShowDeleteDialog(false);\r\n\r\n    try {\r\n      if (userType === 'class') {\r\n        await deleteAllClassNotifications();\r\n      } else {\r\n        await deleteAllStudentNotifications();\r\n      }\r\n\r\n      // Update local state\r\n      setNotifications([]);\r\n      setUnreadCount(0);\r\n      toast.success('All notifications removed successfully');\r\n    } catch (error) {\r\n      console.error('Error removing all notifications:', error);\r\n      toast.error('Failed to remove all notifications');\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchNotifications();\r\n\r\n    const interval = setInterval(fetchNotifications, 30000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [fetchNotifications]);\r\n\r\n  return (\r\n    <>\r\n      <Popover open={isOpen} onOpenChange={setIsOpen}>\r\n        <PopoverTrigger asChild>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"relative bg-black hover:bg-gray-900 transition duration-200 h-9 w-9 md:h-10 md:w-10 rounded-full\"\r\n          >\r\n            <div className=\"absolute inset-0 rounded-full bg-orange-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none\" />\r\n\r\n            <Bell className=\"relative z-10 h-4 w-4 md:h-5 md:w-5 text-white transition-colors duration-200\" />\r\n\r\n            {unreadCount > 0 && (\r\n              <div className=\"absolute -top-1.5 -right-1.5 md:-top-2 md:-right-2 h-5 w-5 bg-red-500 rounded-full flex items-center justify-center z-20\">\r\n                <span className=\"text-white text-[10px] md:text-xs font-semibold leading-none\">\r\n                  {unreadCount > 99 ? '99+' : unreadCount}\r\n                </span>\r\n              </div>\r\n            )}\r\n          </Button>\r\n        </PopoverTrigger>\r\n        <PopoverContent className=\"w-80 p-0\" align=\"end\">\r\n          <div className=\"p-4 border-b\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <h3 className=\"font-semibold\">Notifications</h3>\r\n              <div className=\"flex gap-2\">\r\n                {unreadCount > 0 && (\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"sm\"\r\n                    onClick={handleMarkAllAsRead}\r\n                    className=\"text-xs\"\r\n                  >\r\n                    Mark all read\r\n                  </Button>\r\n                )}\r\n                {notifications.length > 0 && unreadCount === 0 && (\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"sm\"\r\n                    onClick={handleRemoveAllClick}\r\n                    className=\"text-xs text-red-600 hover:text-red-700 hover:bg-red-50\"\r\n                  >\r\n                    Remove all\r\n                  </Button>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"h-80 overflow-y-auto\">\r\n            {loading ? (\r\n              <div className=\"p-4 text-center text-muted-foreground\">\r\n                Loading notifications...\r\n              </div>\r\n            ) : notifications.length === 0 ? (\r\n              <div className=\"p-4 text-center text-muted-foreground\">\r\n                No notifications yet\r\n              </div>\r\n            ) : (\r\n              <div className=\"divide-y\">\r\n                {Array.isArray(notifications) && notifications.map((notification) => (\r\n                  <div\r\n                    key={notification.id}\r\n                    className={`p-4 cursor-pointer hover:bg-muted/50 transition-colors ${!notification.isRead ? 'bg-blue-50/50' : ''\r\n                      }`}\r\n                    onClick={() => handleNotificationClick(notification)}\r\n                  >\r\n                    <div className=\"flex items-start gap-3\">\r\n                      <div className={`w-2 h-2 rounded-full mt-2 ${!notification.isRead ? 'bg-blue-500' : 'bg-gray-300'\r\n                        }`} />\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <p className=\"font-medium text-sm\">{notification.title}</p>\r\n                        <p className=\"text-sm text-muted-foreground mt-1\">\r\n                          {notification.message}\r\n                        </p>\r\n                        <p className=\"text-xs text-muted-foreground mt-2\">\r\n                          {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n          {safeNotifications.length > 0 && (\r\n            <div className=\"p-3 border-t bg-muted/30\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                className=\"w-full text-xs\"\r\n                onClick={() => {\r\n                  setIsOpen(false);\r\n                  router.push('/notifications');\r\n                }}\r\n              >\r\n                View All Notifications\r\n              </Button>\r\n            </div>\r\n          )}\r\n        </PopoverContent>\r\n      </Popover>\r\n\r\n      {/* Delete Confirmation Dialog */}\r\n      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Remove All Notifications</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Are you sure you want to remove all notifications? This action cannot be undone.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel>Cancel</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={handleConfirmRemoveAll}\r\n              className=\"bg-red-600 hover:bg-red-700\"\r\n            >\r\n              Remove All\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAMA;AAUA;AAaA;AACA;AACA;AApCA;;;;;;;;;;;AA0Ce,SAAS,iBAAiB,EAAE,QAAQ,EAAyB;IAC1E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,oBAAoB,MAAM,OAAO,CAAC,iBAAiB,gBAAgB,EAAE;IAE3E,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI;YACF,WAAW;YACX,IAAI;YACJ,IAAI;YAEJ,IAAI,aAAa,SAAS;gBACxB,SAAS,MAAM,CAAA,GAAA,sIAAA,CAAA,wBAAqB,AAAD,EAAE,GAAG;gBACxC,QAAQ,MAAM,CAAA,GAAA,sIAAA,CAAA,sBAAmB,AAAD;YAClC,OAAO;gBACL,SAAS,MAAM,CAAA,GAAA,sIAAA,CAAA,0BAAuB,AAAD,EAAE,GAAG;gBAC1C,QAAQ,MAAM,CAAA,GAAA,sIAAA,CAAA,wBAAqB,AAAD;YACpC;YAEA,2CAA2C;YAC3C,MAAM,SAAS,QAAQ,iBAAiB,UAAU,EAAE;YACpD,iBAAiB,MAAM,OAAO,CAAC,UAAU,SAAS,EAAE;YACpD,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,iBAAiB,EAAE;YACnB,eAAe;QACjB,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,0BAA0B,OAAO;QACrC,IAAI;YACF,4BAA4B;YAC5B,IAAI,aAAa,SAAS;gBACxB,MAAM,CAAA,GAAA,sIAAA,CAAA,8BAA2B,AAAD,EAAE,aAAa,EAAE;YACnD,OAAO;gBACL,MAAM,CAAA,GAAA,sIAAA,CAAA,gCAA6B,AAAD,EAAE,aAAa,EAAE;YACrD;YAEA,qBAAqB;YACrB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,QACP,MAAM,EAAE,KAAK,aAAa,EAAE,GAAG;wBAAE,GAAG,KAAK;wBAAE,QAAQ;oBAAK,IAAI;YAGhE,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;YAC1C,UAAU;YACV,IAAI,aAAa,IAAI,EAAE,eAAe,eAAe,aAAa,IAAI,EAAE,aAAa;gBACnF,OAAO,IAAI,CAAC,aAAa,IAAI,CAAC,WAAW;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI;YACF,IAAI,aAAa,SAAS;gBACxB,MAAM,CAAA,GAAA,sIAAA,CAAA,kCAA+B,AAAD;YACtC,OAAO;gBACL,MAAM,CAAA,GAAA,sIAAA,CAAA,oCAAiC,AAAD;YACxC;YAEA,qBAAqB;YACrB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,QAAS,CAAC;wBAAE,GAAG,KAAK;wBAAE,QAAQ;oBAAK,CAAC;YAE/C,eAAe;YACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,uBAAuB;QAC3B,oBAAoB;IACtB;IAEA,MAAM,yBAAyB;QAC7B,oBAAoB;QAEpB,IAAI;YACF,IAAI,aAAa,SAAS;gBACxB,MAAM,CAAA,GAAA,sIAAA,CAAA,8BAA2B,AAAD;YAClC,OAAO;gBACL,MAAM,CAAA,GAAA,sIAAA,CAAA,gCAA6B,AAAD;YACpC;YAEA,qBAAqB;YACrB,iBAAiB,EAAE;YACnB,eAAe;YACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QAEA,MAAM,WAAW,YAAY,oBAAoB;QAEjD,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAmB;IAEvB,qBACE;;0BACE,8OAAC,mIAAA,CAAA,UAAO;gBAAC,MAAM;gBAAQ,cAAc;;kCACnC,8OAAC,mIAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAEf,cAAc,mBACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;kCAMtC,8OAAC,mIAAA,CAAA,iBAAc;wBAAC,WAAU;wBAAW,OAAM;;0CACzC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAgB;;;;;;sDAC9B,8OAAC;4CAAI,WAAU;;gDACZ,cAAc,mBACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;gDAIF,cAAc,MAAM,GAAG,KAAK,gBAAgB,mBAC3C,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;0CAOT,8OAAC;gCAAI,WAAU;0CACZ,wBACC,8OAAC;oCAAI,WAAU;8CAAwC;;;;;2CAGrD,cAAc,MAAM,KAAK,kBAC3B,8OAAC;oCAAI,WAAU;8CAAwC;;;;;yDAIvD,8OAAC;oCAAI,WAAU;8CACZ,MAAM,OAAO,CAAC,kBAAkB,cAAc,GAAG,CAAC,CAAC,6BAClD,8OAAC;4CAEC,WAAW,CAAC,uDAAuD,EAAE,CAAC,aAAa,MAAM,GAAG,kBAAkB,IAC1G;4CACJ,SAAS,IAAM,wBAAwB;sDAEvC,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,0BAA0B,EAAE,CAAC,aAAa,MAAM,GAAG,gBAAgB,eAChF;;;;;;kEACJ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAuB,aAAa,KAAK;;;;;;0EACtD,8OAAC;gEAAE,WAAU;0EACV,aAAa,OAAO;;;;;;0EAEvB,8OAAC;gEAAE,WAAU;0EACV,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,aAAa,SAAS,GAAG;oEAAE,WAAW;gEAAK;;;;;;;;;;;;;;;;;;2CAd1E,aAAa,EAAE;;;;;;;;;;;;;;;4BAuB7B,kBAAkB,MAAM,GAAG,mBAC1B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;wCACP,UAAU;wCACV,OAAO,IAAI,CAAC;oCACd;8CACD;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC,2IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAkB,cAAc;0BACjD,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,8OAAC,2IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAI1B,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 888, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/avatar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as AvatarPrimitive from '@radix-ui/react-avatar';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Avatar({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn('relative flex size-8 shrink-0 overflow-hidden rounded-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarImage({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn('aspect-square size-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn('bg-muted flex size-full items-center justify-center rounded-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback };\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EAAE,SAAS,EAAE,GAAG,OAA0D;IACxF,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8DAA8D;QAC3E,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC9F,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oEAAoE;QACjF,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/studentAuthServices.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\nimport { GoogleAuthData } from '@/lib/types';\r\n\r\ninterface StudentRegisterData {\r\n  firstName: string;\r\n  lastName: string;\r\n  contactNo: string;\r\n  referralCode?: string;\r\n}\r\n\r\ninterface StudentLoginData {\r\n  contactNo: string;\r\n  email?: string;\r\n}\r\n\r\ninterface VerifyOtpData {\r\n  contactNo: string;\r\n  otp: string;\r\n  firstName?: string;\r\n  lastName?: string;\r\n  email?: string;\r\n}\r\n\r\ninterface ResendOtpData {\r\n  contactNo: string;\r\n  firstName?: string;\r\n}\r\n\r\ninterface ContinueWithEmailData {\r\n  email: string;\r\n}\r\n\r\nexport const continueWithEmail = async (data: ContinueWithEmailData) => {\r\n  const response = await axiosInstance.post('/student/continue-with-email', data);\r\n  return response.data;\r\n};\r\n\r\nexport const registerStudent = async (data: StudentRegisterData) => {\r\n  const response = await axiosInstance.post('/student/register', data);\r\n  return response.data;\r\n};\r\n\r\nexport const loginStudent = async (data: StudentLoginData) => {\r\n  const response = await axiosInstance.post('/student/login', data);\r\n  return response.data;\r\n};\r\n\r\nexport const logoutStudent = async (): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.post('/student/logout');\r\n\r\n    localStorage.removeItem('studentToken');\r\n    localStorage.removeItem('student_data');\r\n\r\n    return response.data;\r\n  } catch {\r\n    localStorage.removeItem('studentToken');\r\n    localStorage.removeItem('student_data');\r\n\r\n    return {\r\n      success: true,\r\n      message: 'Logged out successfully',\r\n    };\r\n  }\r\n};\r\n\r\nexport async function verifyOtp(data: VerifyOtpData) {\r\n  const response = await axiosInstance.post('/student/verify-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function resendOtp(data: ResendOtpData) {\r\n  const response = await axiosInstance.post('/student/resend-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport const googleAuthStudent = async (googleAuthData: GoogleAuthData) => {\r\n    const response = await axiosInstance.post(`/student/google-auth`, googleAuthData);\r\n    return response.data;\r\n};\r\n\r\nexport const studentverifyEmail = async (token: string) => {\r\n    const response = await axiosInstance.post(`/student/verify-email`, { token });\r\n    return response.data;\r\n};\r\n\r\nexport const studentresendVerificationEmail = async (email: string) => {\r\n    const response = await axiosInstance.post(`/student/resend-verification`, { email });\r\n    return response.data;\r\n};"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AAgCO,MAAM,oBAAoB,OAAO;IACtC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,gCAAgC;IAC1E,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,kBAAkB,OAAO;IACpC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,qBAAqB;IAC/D,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,eAAe,OAAO;IACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,kBAAkB;IAC5D,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,gBAAgB;IAC3B,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC;QAE1C,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QAExB,OAAO,SAAS,IAAI;IACtB,EAAE,OAAM;QACN,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QAExB,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF;AACF;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB;IACjE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB;IACjE,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,oBAAoB,OAAO;IACpC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,EAAE;IAClE,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,qBAAqB,OAAO;IACrC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,qBAAqB,CAAC,EAAE;QAAE;IAAM;IAC3E,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,iCAAiC,OAAO;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,4BAA4B,CAAC,EAAE;QAAE;IAAM;IAClF,OAAO,SAAS,IAAI;AACxB", "debugId": null}}, {"offset": {"line": 1010, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/AuthService.ts"], "sourcesContent": ["import { axiosInstance } from '../lib/axios';\r\n\r\ninterface RegisterData {\r\n  firstName: string;\r\n  lastName: string;\r\n  contactNo: string;\r\n  referralCode?: string;\r\n}\r\n\r\ninterface LoginData {\r\n  contactNo: string;\r\n  email?: string;\r\n}\r\n\r\ninterface VerifyOtpData {\r\n  contactNo: string;\r\n  otp: string;\r\n  email?: string;\r\n  firstName?: string;\r\n  lastName?: string;\r\n}\r\n\r\ninterface ResendOtpData {\r\n  contactNo: string;\r\n  firstName?: string;\r\n}\r\n\r\ninterface ContinueWithEmailData {\r\n  email: string;\r\n}\r\n\r\nexport async function continueWithEmail(data: ContinueWithEmailData) {\r\n  const response = await axiosInstance.post('/auth-client/continue-with-email', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function registerUser(data: RegisterData) {\r\n  const response = await axiosInstance.post('/auth-client/register', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function loginUser(data: LoginData) {\r\n  const response = await axiosInstance.post('/auth-client/login', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function verifyOtp(data: VerifyOtpData) {\r\n  const response = await axiosInstance.post('/auth-client/verify-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function resendOtp(data: ResendOtpData) {\r\n  const response = await axiosInstance.post('/auth-client/resend-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport function logoutUser(): void {\r\n  localStorage.removeItem('user');\r\n}\r\n\r\nexport const generateJWT = async (contact: string | undefined, password : string | undefined) => {\r\n  const response = await axiosInstance.post(`/auth-client/generate-jwt`, { contact, password });\r\n  return response.data;\r\n};\r\n\r\n\r\n\r\nexport const verifyEmail = async (token: string) => {\r\n  const response = await axiosInstance.get(`/auth-client/verify-email`, { params: { token } });\r\n  return response.data;\r\n};\r\n\r\nexport const resendVerificationEmail = async (email: string) => {\r\n  const response = await axiosInstance.post(`/auth-client/resend-verification`, { email });\r\n  return response.data;\r\n};"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AA+BO,eAAe,kBAAkB,IAA2B;IACjE,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,oCAAoC;IAC9E,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,aAAa,IAAkB;IACnD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,yBAAyB;IACnE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAe;IAC7C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,sBAAsB;IAChE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,2BAA2B;IACrE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,2BAA2B;IACrE,OAAO,SAAS,IAAI;AACtB;AAEO,SAAS;IACd,aAAa,UAAU,CAAC;AAC1B;AAEO,MAAM,cAAc,OAAO,SAA6B;IAC7D,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,yBAAyB,CAAC,EAAE;QAAE;QAAS;IAAS;IAC3F,OAAO,SAAS,IAAI;AACtB;AAIO,MAAM,cAAc,OAAO;IAChC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,yBAAyB,CAAC,EAAE;QAAE,QAAQ;YAAE;QAAM;IAAE;IAC1F,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,0BAA0B,OAAO;IAC5C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,gCAAgC,CAAC,EAAE;QAAE;IAAM;IACtF,OAAO,SAAS,IAAI;AACtB", "debugId": null}}, {"offset": {"line": 1073, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/mockExamStreakApi.ts"], "sourcesContent": ["import { axiosInstance } from \"../lib/axios\";\r\n\r\nexport const saveMockExamStreak = async (studentId: string): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.put(`/mock-exam-streak/${studentId}`, {}, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return { success: true, data: response.data.data };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to save mock exam streak: ${\r\n        error.response?.data?.error || error.message\r\n      }`,\r\n    };\r\n  }\r\n};\r\n\r\nexport const getMockExamStreak = async (studentId: string): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.get(`/mock-exam-streak/${studentId}`, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return { success: true, data: response.data.data }; \r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to get mock exam streak: ${\r\n        error.response?.data?.error || error.message\r\n      }`,\r\n    };\r\n  }\r\n};"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,qBAAqB,OAAO;IACvC,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,WAAW,EAAE,CAAC,GAAG;YAC7E,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI,CAAC,IAAI;QAAC;IACnD,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,iCAAiC,EACvC,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,EAC5C;QACJ;IACF;AACF;AAEO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,WAAW,EAAE;YACzE,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI,CAAC,IAAI;QAAC;IACnD,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,gCAAgC,EACtC,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,EAC5C;QACJ;IACF;AACF", "debugId": null}}, {"offset": {"line": 1121, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/streakcountdisplay.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { getMockExamStreak } from '@/services/mockExamStreakApi';\r\n\r\n\r\ninterface MockExamStreakResponse {\r\n  success: boolean;\r\n  data?: { streak: number; lastAttempt: string | null };\r\n  error?: string;\r\n}\r\n\r\ninterface StreakDisplayProps {\r\n  studentId?: string;\r\n}\r\n\r\nconst StreakDisplay: React.FC<StreakDisplayProps> = ({ studentId }) => {\r\n  const [streak, setStreak] = useState<number>(0);\r\n\r\n  useEffect(() => {\r\n    const fetchStreak = async () => {\r\n      if (!studentId) {\r\n        setStreak(0);\r\n        return;\r\n      }\r\n      const response: MockExamStreakResponse = await getMockExamStreak(studentId);\r\n      if (response.success && response.data) {\r\n        setStreak(response.data.streak || 0);\r\n      } else {\r\n        setStreak(0);\r\n      }\r\n    };\r\n    fetchStreak();\r\n  }, [studentId]);\r\n\r\n  return (\r\n       <span className=\"bg-black  text-white text-l rounded-full px-2 py-1 flex items-center gap-1\">\r\n      🔥 {streak}\r\n    </span>\r\n  );\r\n};\r\n\r\nexport default StreakDisplay;"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAaA,MAAM,gBAA8C,CAAC,EAAE,SAAS,EAAE;IAChE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,IAAI,CAAC,WAAW;gBACd,UAAU;gBACV;YACF;YACA,MAAM,WAAmC,MAAM,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD,EAAE;YACjE,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,UAAU,SAAS,IAAI,CAAC,MAAM,IAAI;YACpC,OAAO;gBACL,UAAU;YACZ;QACF;QACA;IACF,GAAG;QAAC;KAAU;IAEd,qBACK,8OAAC;QAAK,WAAU;;YAA6E;YAC1F;;;;;;;AAGV;uCAEe", "debugId": null}}, {"offset": {"line": 1168, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/Header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport {\r\n  <PERSON>u,\r\n  X,\r\n  User,\r\n  ShoppingBag,\r\n  Share2,\r\n  UserCircle,\r\n  LayoutDashboard,\r\n  MessageSquare,\r\n  Coins,\r\n  BadgeCent,\r\n} from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { RootState } from \"@/store\";\r\nimport { useAppDispatch } from \"@/store/hooks\";\r\nimport { useEffect, useState, useRef } from \"react\";\r\nimport { isStudentAuthenticated, clearStudentAuthToken } from \"@/lib/utils\";\r\nimport ProfileCompletionIndicator from \"./ProfileCompletionIndicator\";\r\nimport NotificationBell from \"./NotificationBell\";\r\nimport { Avatar, AvatarFallback } from \"@/components/ui/avatar\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\nimport { toast } from \"sonner\";\r\nimport { logoutStudent } from \"@/services/studentAuthServices\";\r\nimport { clearUser } from \"@/store/slices/userSlice\";\r\nimport { clearStudentProfileData } from \"@/store/slices/studentProfileSlice\";\r\nimport { fetchStudentProfile } from \"@/store/thunks/studentProfileThunks\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { axiosInstance } from \"@/lib/axios\";\r\nimport { useMotionValue, useAnimationFrame } from \"framer-motion\";\r\nimport { generateJWT } from \"@/services/AuthService\";\r\nimport StreakDisplay from \"@/components/ui/streakcountdisplay\";\r\n\r\nconst Header = () => {\r\n  const { isAuthenticated, user } = useSelector(\r\n    (state: RootState) => state.user\r\n  );\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n  const [isStudentLoggedIn, setIsStudentLoggedIn] = useState(false);\r\n  const [studentData, setStudentData] = useState<any>(null);\r\n  const [classStatus, setClassStatus] = useState<string | null>(null);\r\n  const dispatch = useAppDispatch();\r\n  const router = useRouter();\r\n\r\n  const contentRef = useRef<HTMLDivElement>(null);\r\n  const [contentWidth, setContentWidth] = useState(0);\r\n  const x = useMotionValue(0);\r\n  const speed = contentWidth / 20;\r\n\r\n  useEffect(() => {\r\n    const isLoggedIn = isStudentAuthenticated();\r\n    setIsStudentLoggedIn(isLoggedIn);\r\n\r\n    if (isLoggedIn) {\r\n      const storedData = localStorage.getItem(\"student_data\");\r\n      if (storedData) {\r\n        setStudentData(JSON.parse(storedData));\r\n      }\r\n      dispatch(fetchStudentProfile());\r\n    }\r\n    const fetchClassStatus = async () => {\r\n      if (isAuthenticated && user?.id) {\r\n        try {\r\n          const response = await axiosInstance.get(`/classes/details/${user.id}`);\r\n          if (response.data && response.data.status) {\r\n            setClassStatus(response.data.status.status);\r\n          }\r\n        } catch (error) {\r\n          console.error('Error fetching class status:', error);\r\n        }\r\n      }\r\n    };\r\n\r\n    fetchClassStatus();\r\n\r\n    const handleStorageChange = () => {\r\n      const newLoginStatus = isStudentAuthenticated();\r\n      setIsStudentLoggedIn(newLoginStatus);\r\n      if (newLoginStatus) {\r\n        const storedData = localStorage.getItem(\"student_data\");\r\n        if (storedData) {\r\n          setStudentData(JSON.parse(storedData));\r\n        }\r\n        dispatch(fetchStudentProfile());\r\n      } else {\r\n        setStudentData(null);\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"storage\", handleStorageChange);\r\n\r\n    if (contentRef.current) {\r\n      const width = contentRef.current.getBoundingClientRect().width;\r\n      setContentWidth(width);\r\n    }\r\n\r\n    return () => {\r\n      window.removeEventListener(\"storage\", handleStorageChange);\r\n    };\r\n  }, [dispatch]);\r\n\r\n  useAnimationFrame((time, delta) => {\r\n    if (contentWidth === 0) return;\r\n    const currentX = x.get();\r\n    const deltaX = (speed * delta) / 1000;\r\n    let newX = currentX - deltaX;\r\n    if (newX <= -contentWidth) {\r\n      newX = 0;\r\n    }\r\n    x.set(newX);\r\n  });\r\n\r\n  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);\r\n\r\n  const handleStudentLogout = async () => {\r\n    try {\r\n      const response = await logoutStudent();\r\n      if (response.success !== false) {\r\n        clearStudentAuthToken();\r\n        setIsStudentLoggedIn(false);\r\n        setStudentData(null);\r\n        localStorage.removeItem(\"student_data\");\r\n        dispatch(clearStudentProfileData());\r\n        toast.success(\"Logged out successfully\");\r\n        window.dispatchEvent(new Event(\"storage\"));\r\n      } else {\r\n        toast.error(response.message || \"Failed to logout\");\r\n        clearStudentAuthToken();\r\n        setIsStudentLoggedIn(false);\r\n        setStudentData(null);\r\n        localStorage.removeItem(\"student_data\");\r\n        dispatch(clearStudentProfileData());\r\n      }\r\n    } catch (error) {\r\n      console.log(\"Failed to logout\", error);\r\n      toast.error(\"Failed to logout\");\r\n      localStorage.removeItem(\"student_data\");\r\n      clearStudentAuthToken();\r\n      setIsStudentLoggedIn(false);\r\n      setStudentData(null);\r\n      dispatch(clearStudentProfileData());\r\n    }\r\n  };\r\n\r\n  const accessClassDashboard = async () => {\r\n    try {\r\n      const response = await generateJWT(user?.contactNo, user?.password);\r\n\r\n      if (response.success) {\r\n        const { token } = response.data;\r\n        const redirectUrl = `${process.env.NEXT_PUBLIC_RANNDASS_URL}/login-class-link?uid=${user?.id}&token=${token}`;\r\n        window.location.href = redirectUrl;\r\n      } else {\r\n        toast.error(response.message || \"Failed to generate token\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Failed to generate token\", error);\r\n      toast.error(\"Failed to generate token\");\r\n    }\r\n  };\r\n\r\n  const navLinks = [\r\n    { href: \"/verified-classes\", label: \"Find Tutor\" },\r\n    { href: \"/uwhiz\", label: \"U - Whiz\" },\r\n    {\r\n      href: \"/mock-exam-card\",\r\n      label: (\r\n        <span className=\"flex items-center gap-2\">\r\n          <span>Daily Quiz</span>\r\n          {isStudentLoggedIn && <StreakDisplay studentId={studentData?.id} />}\r\n        </span>\r\n      ),\r\n      isNew: true\r\n    },\r\n    { href: \"/careers\", label: \"Career\" },\r\n    { href: \"/store\", label: \"Store\" },\r\n  ];\r\n\r\n  const classMenuItems = [\r\n    {\r\n      href: \"/classes/profile\",\r\n      icon: <UserCircle className=\"w-5 h-5 mr-2\" />,\r\n      label: \"Profile\",\r\n    },\r\n    {\r\n      href: \"/classes/chat\",\r\n      icon: <MessageSquare className=\"w-5 h-5 mr-2\" />,\r\n      label: \"Messages\",\r\n    },\r\n    {\r\n      href: \"/coins\",\r\n      icon: <Coins className=\"w-5 h-5 mr-2\" />,\r\n      label: \"Coins\",\r\n    },\r\n    {\r\n      href: \"/classes/my-orders\",\r\n      icon: <ShoppingBag className=\"w-5 h-5 mr-2\" />,\r\n      label: \"My Orders\",\r\n    },\r\n    {\r\n      onClick: accessClassDashboard,\r\n      icon: <LayoutDashboard className=\"w-5 h-5 mr-2\" />,\r\n      label: \"My Dashboard\",\r\n    },\r\n    {\r\n      href: \"/classes/referral-dashboard\",\r\n      icon: <Share2 className=\"w-5 h-5 mr-2\" />,\r\n      label: \"Referral Dashboard\",\r\n    },\r\n    ...(classStatus === 'APPROVED' ? [{\r\n      href: \"/classes/payment\",\r\n      icon: <BadgeCent className=\"w-5 h-5 mr-2\" />,\r\n      label: \"Payment Details\",\r\n    }] : [])\r\n  ];\r\n\r\n  return (\r\n    <>\r\n      <header className=\"sticky top-0 z-50 w-full bg-black\">\r\n        <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"flex h-16 items-center justify-between\">\r\n            <Link\r\n              href=\"/\"\r\n              className=\"flex items-center space-x-2 transition-transform hover:scale-105\"\r\n            >\r\n              <Image\r\n                src=\"/logo_black.png\"\r\n                alt=\"Preply Logo\"\r\n                width={120}\r\n                height={40}\r\n                className=\"rounded-sm\"\r\n              />\r\n            </Link>\r\n\r\n            <nav className=\"hidden md:flex items-center space-x-6\">\r\n              {navLinks.map((link) => (\r\n                <Link\r\n                  key={link.href}\r\n                  href={link.href}\r\n                  className=\"relative flex items-center gap-2 border border-gray-700 rounded-md px-4 py-2 text-sm font-medium text-gray-300 transition-all hover:border-orange-500 hover:text-orange-400\"\r\n                >\r\n                  {link.label}\r\n                  {link.isNew && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white animate-pulse\">\r\n                      Trending\r\n                    </span>\r\n                  )}\r\n                </Link>\r\n              ))}\r\n            </nav>\r\n\r\n            <div className=\"flex items-center space-x-3\">\r\n              {isAuthenticated || isStudentLoggedIn ? (\r\n                <>\r\n                  <NotificationBell\r\n                    userType={isAuthenticated ? \"class\" : \"student\"}\r\n                  />\r\n                  <Popover>\r\n                    <PopoverTrigger asChild>\r\n                      <Avatar className=\"cursor-pointer h-9 w-9 hover:opacity-80 transition-opacity\">\r\n                        <AvatarFallback className=\"bg-white text-black flex items-center justify-center text-sm font-semibold\">\r\n                          {isAuthenticated\r\n                            ? user?.firstName && user?.lastName\r\n                              ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()\r\n                              : \"CT\"\r\n                            : studentData?.firstName && studentData?.lastName\r\n                            ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()\r\n                            : \"ST\"}\r\n                        </AvatarFallback>\r\n                      </Avatar>\r\n                    </PopoverTrigger>\r\n                    <PopoverContent className=\"w-64 bg-white p-4 rounded-lg shadow-lg\">\r\n                      <div className=\"flex items-center gap-3 mb-4\">\r\n                        <Avatar className=\"h-10 w-10\">\r\n                          <AvatarFallback className=\"bg-white text-black\">\r\n                            {isAuthenticated\r\n                              ? user?.firstName && user?.lastName\r\n                                ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()\r\n                                : \"CT\"\r\n                              : studentData?.firstName && studentData?.lastName\r\n                              ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()\r\n                              : \"ST\"}\r\n                          </AvatarFallback>\r\n                        </Avatar>\r\n                        <div>\r\n                          <p className=\"font-medium text-black\">\r\n                            {isAuthenticated\r\n                              ? user?.firstName && user?.lastName\r\n                                ? `${user.firstName} ${user.lastName}`\r\n                                : user?.className || \"Class Account\"\r\n                              : studentData?.firstName && studentData?.lastName\r\n                              ? `${studentData.firstName} ${studentData.lastName}`\r\n                              : \"Student Account\"}\r\n                          </p>\r\n                          <p className=\"text-xs text-gray-600\">\r\n                            {isAuthenticated\r\n                              ? user?.contactNo || \"<EMAIL>\"\r\n                              : studentData?.contactNo || \"<EMAIL>\"}\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div className=\"space-y-2\">\r\n                        {isAuthenticated ? (\r\n                          <>\r\n                            {classMenuItems.map((item) => (\r\n                              <Button\r\n                                asChild\r\n                                variant=\"ghost\"\r\n                                className=\"w-full justify-start px-4 py-2 hover:bg-muted rounded-md transition text-sm font-medium text-foreground\"\r\n                                key={item.href || item.label}\r\n                              >\r\n                                {item.href ? (\r\n                                  <Link\r\n                                    href={item.href}\r\n                                    className=\"flex items-center\"\r\n                                  >\r\n                                    {item.icon}\r\n                                    <span>{item.label}</span>\r\n                                  </Link>\r\n                                ) : (\r\n                                  <div\r\n                                    onClick={item.onClick}\r\n                                    className=\"flex items-center w-full\"\r\n                                  >\r\n                                    {item.icon}\r\n                                    <span>{item.label}</span>\r\n                                  </div>\r\n                                )}\r\n                              </Button>\r\n                            ))}\r\n                            <Button\r\n                              variant=\"ghost\"\r\n                              className=\"w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md\"\r\n                              onClick={async () => {\r\n                                try {\r\n                                  const response = await axiosInstance.post(\r\n                                    \"/auth-client/logout\",\r\n                                    {}\r\n                                  );\r\n                                  if (response.data.success) {\r\n                                    router.push(\"/\");\r\n                                    dispatch(clearUser());\r\n                                    localStorage.removeItem(\"token\");\r\n                                    toast.success(\"Logged out successfully\");\r\n                                  }\r\n                                } catch (error) {\r\n                                  console.error(\"Logout error:\", error);\r\n                                  toast.error(\"Failed to logout\");\r\n                                }\r\n                              }}\r\n                            >\r\n                              <User className=\"w-5 h-5 mr-2\" />\r\n                              <span>Logout</span>\r\n                            </Button>\r\n                          </>\r\n                        ) : (\r\n                          <>\r\n                            <div className=\"space-y-2\">\r\n                              {[\r\n                                {\r\n                                  href: \"/student/profile\",\r\n                                  icon: <UserCircle className=\"w-5 h-5 mr-2\" />,\r\n                                  label: \"Profile\",\r\n                                },\r\n                                {\r\n                                  href: \"/student/chat\",\r\n                                  icon: (\r\n                                    <MessageSquare className=\"w-5 h-5 mr-2\" />\r\n                                  ),\r\n                                  label: \"Messages\",\r\n                                },\r\n                                {\r\n                                  href: \"/coins\",\r\n                                  icon: <Coins className=\"w-5 h-5 mr-2\" />,\r\n                                  label: \"Coins\",\r\n                                },\r\n                                {\r\n                                  href: \"/student/wishlist\",\r\n                                  icon: (\r\n                                    <ShoppingBag className=\"w-5 h-5 mr-2\" />\r\n                                  ),\r\n                                  label: \"My Wishlist\",\r\n                                },\r\n                                {\r\n                                  href: \"/student/referral-dashboard\",\r\n                                  icon: <Share2 className=\"w-5 h-5 mr-2\" />,\r\n                                  label: \"Referral Dashboard\",\r\n                                },\r\n                                {\r\n                                  href: \"/student/my-orders\",\r\n                                  icon: (\r\n                                    <ShoppingBag className=\"w-5 h-5 mr-2\" />\r\n                                  ),\r\n                                  label: \"My Orders\",\r\n                                },\r\n                              ].map((item) => (\r\n                                <Button\r\n                                  asChild\r\n                                  variant=\"ghost\"\r\n                                  className=\"w-full justify-start px-4 py-2 hover:bg-muted rounded-md transition text-sm font-medium text-foreground\"\r\n                                  key={item.href}\r\n                                >\r\n                                  <Link\r\n                                    href={item.href}\r\n                                    className=\"flex items-center\"\r\n                                  >\r\n                                    {item.icon}\r\n                                    <span>{item.label}</span>\r\n                                  </Link>\r\n                                </Button>\r\n                              ))}\r\n                              <Button\r\n                                onClick={handleStudentLogout}\r\n                                className=\"w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md\"\r\n                              >\r\n                                <User className=\"w-5 h-5 mr-2\" />\r\n                                <span>Logout</span>\r\n                              </Button>\r\n                            </div>\r\n                          </>\r\n                        )}\r\n                      </div>\r\n                    </PopoverContent>\r\n                  </Popover>\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <div className=\"hidden md:flex items-center gap-2\">\r\n                    <Button\r\n                      className=\"bg-[#ff914d] hover:bg-[#E88143] text-white text-sm px-4 py-2 rounded-md\"\r\n                      asChild\r\n                    >\r\n                      <Link href=\"/class/login\">Join as Tutor</Link>\r\n                    </Button>\r\n\r\n                    <Button\r\n                      variant=\"ghost\"\r\n                      className=\"bg-black text-white text-sm px-4 py-2 rounded-md border border-gray-700\"\r\n                      asChild\r\n                    >\r\n                      <Link href=\"/student/login\">Student Login</Link>\r\n                    </Button>\r\n                  </div>\r\n                </>\r\n              )}\r\n\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"md:hidden text-orange-400 hover:bg-orange-500/10 rounded-full\"\r\n                onClick={toggleMenu}\r\n              >\r\n                {isMenuOpen ? (\r\n                  <X className=\"h-6 w-6\" />\r\n                ) : (\r\n                  <Menu className=\"h-6 w-6\" />\r\n                )}\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      <div>\r\n        <div\r\n          className={`fixed inset-y-0 right-0 z-50 w-80 bg-black transform transition-all duration-300 ease-in-out md:hidden ${\r\n            isMenuOpen ? \"translate-x-0\" : \"translate-x-full\"\r\n          }`}\r\n        >\r\n          <div className=\"flex flex-col h-full p-6\">\r\n            <div className=\"flex items-center justify-between mb-6\">\r\n              <Image\r\n                src=\"/logo_black.png\"\r\n                alt=\"Uest Logo\"\r\n                width={100}\r\n                height={32}\r\n                className=\"rounded-sm\"\r\n              />\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"text-orange-400 hover:bg-orange-500/10 rounded-full\"\r\n                onClick={toggleMenu}\r\n              >\r\n                <X className=\"h-6 w-6\" />\r\n              </Button>\r\n            </div>\r\n\r\n            {(isAuthenticated || isStudentLoggedIn) && (\r\n              <div className=\"mb-6\">\r\n                <div className=\"flex items-center gap-3 p-3 bg-gray-900 rounded-lg\">\r\n                  <Avatar className=\"h-10 w-10\">\r\n                    <AvatarFallback className=\"bg-white text-black\">\r\n                      {isAuthenticated\r\n                        ? user?.firstName && user?.lastName\r\n                          ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()\r\n                          : \"CT\"\r\n                        : studentData?.firstName && studentData?.lastName\r\n                        ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()\r\n                        : \"ST\"}\r\n                    </AvatarFallback>\r\n                  </Avatar>\r\n                  <div>\r\n                    <p className=\"font-medium text-white\">\r\n                      {isAuthenticated\r\n                        ? user?.firstName && user?.lastName\r\n                          ? `${user.firstName} ${user.lastName}`\r\n                          : user?.className || \"Class Account\"\r\n                        : studentData?.firstName && studentData?.lastName\r\n                        ? `${studentData.firstName} ${studentData.lastName}`\r\n                        : \"Student Account\"}\r\n                    </p>\r\n                    <p className=\"text-xs text-gray-400\">\r\n                      {isAuthenticated\r\n                        ? user?.contactNo || \"<EMAIL>\"\r\n                        : studentData?.contactNo || \"<EMAIL>\"}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            <nav className=\"flex flex-col space-y-2\">\r\n              {navLinks.map((link) => (\r\n                <Link\r\n                  key={link.href}\r\n                  href={link.href}\r\n                  className=\"flex items-center justify-between gap-3 px-4 py-3 text-base font-medium text-gray-300 hover:text-orange-400 hover:bg-gray-900 rounded-md border border-gray-700 transition-colors\"\r\n                  onClick={toggleMenu}\r\n                >\r\n                  <div className=\"flex items-center gap-3\">\r\n                    {typeof link.label === \"string\" ? (\r\n                      <span>{link.label}</span>\r\n                    ) : (\r\n                      link.label\r\n                    )}\r\n                  </div>\r\n                  {link.isNew && (\r\n                    <span className=\"text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white\">\r\n                      Trending\r\n                    </span>\r\n                  )}\r\n                </Link>\r\n              ))}\r\n            </nav>\r\n\r\n            <div className=\"mt-auto space-y-2\">\r\n              {isAuthenticated && (\r\n                <>\r\n                  {classMenuItems.map((item) => (\r\n                    <Button\r\n                      asChild\r\n                      variant=\"ghost\"\r\n                      className=\"w-full justify-start px-4 py-2 hover:bg-muted rounded-md transition text-sm font-medium text-foreground\"\r\n                      key={item.href || item.label}\r\n                    >\r\n                      {item.href ? (\r\n                        <Link\r\n                          href={item.href}\r\n                          className=\"flex items-center\"\r\n                          onClick={toggleMenu}\r\n                        >\r\n                          {item.icon}\r\n                          <span>{item.label}</span>\r\n                        </Link>\r\n                      ) : (\r\n                        <div\r\n                          onClick={() => {\r\n                            toggleMenu();\r\n                          }}\r\n                          className=\"flex items-center w-full\"\r\n                        >\r\n                          {item.icon}\r\n                          <span>{item.label}</span>\r\n                        </div>\r\n                      )}\r\n                    </Button>\r\n                  ))}\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    className=\"w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md\"\r\n                    onClick={async () => {\r\n                      try {\r\n                        const response = await axiosInstance.post(\r\n                          \"/auth-client/logout\",\r\n                          {}\r\n                        );\r\n                        if (response.data.success) {\r\n                          router.push(\"/\");\r\n                          dispatch(clearUser());\r\n                          localStorage.removeItem(\"token\");\r\n                          toast.success(\"Logged out successfully\");\r\n                        }\r\n                      } catch (error) {\r\n                        console.error(\"Logout error:\", error);\r\n                        toast.error(\"Failed to logout\");\r\n                      }\r\n                      toggleMenu();\r\n                    }}\r\n                  >\r\n                    <User className=\"w-5 h-5 mr-2\" />\r\n                    <span>Logout</span>\r\n                  </Button>\r\n                </>\r\n              )}\r\n\r\n              {isStudentLoggedIn && (\r\n                <>\r\n                  {[\r\n                    {\r\n                      href: \"/student/profile\",\r\n                      icon: <UserCircle className=\"w-5 h-5 mr-2\" />,\r\n                      label: \"Profile\",\r\n                    },\r\n                    {\r\n                      href: \"/student/chat\",\r\n                      icon: <MessageSquare className=\"w-5 h-5 mr-2\" />,\r\n                      label: \"Messages\",\r\n                    },\r\n                    {\r\n                      href: \"/coins\",\r\n                      icon: <Coins className=\"w-5 h-5 mr-2\" />,\r\n                      label: \"Coins\",\r\n                    },\r\n                    {\r\n                      href: \"/student/wishlist\",\r\n                      icon: <ShoppingBag className=\"w-5 h-5 mr-2\" />,\r\n                      label: \"My Wishlist\",\r\n                    },\r\n                    {\r\n                      href: \"/student/referral-dashboard\",\r\n                      icon: <Share2 className=\"w-5 h-5 mr-2\" />,\r\n                      label: \"Referral Dashboard\",\r\n                    },\r\n                    {\r\n                      href: \"/student/my-orders\",\r\n                      icon: <ShoppingBag className=\"w-5 h-5 mr-2\" />,\r\n                      label: \"My Orders\",\r\n                    },\r\n                  ].map((item) => (\r\n                    <Button\r\n                      asChild\r\n                      variant=\"ghost\"\r\n                      className=\"w-full justify-start px-4 py-2 hover:bg-muted rounded-md transition text-sm font-medium text-foreground \"\r\n                      key={item.href}\r\n                    >\r\n                      <Link\r\n                        href={item.href}\r\n                        className=\"flex items-center\"\r\n                        onClick={toggleMenu}\r\n                      >\r\n                        {item.icon}\r\n                        <span>{item.label}</span>\r\n                      </Link>\r\n                    </Button>\r\n                  ))}\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    className=\"w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md\"\r\n                    onClick={() => {\r\n                      handleStudentLogout();\r\n                      toggleMenu();\r\n                    }}\r\n                  >\r\n                    <User className=\"w-5 h-5 mr-2\" />\r\n                    <span>Logout</span>\r\n                  </Button>\r\n                </>\r\n              )}\r\n\r\n              {!isAuthenticated && !isStudentLoggedIn && (\r\n                <div className=\"space-y-2\">\r\n                  <Button\r\n                    className=\"w-full bg-[#ff914d] hover:bg-[#E88143] text-white rounded-lg py-3\"\r\n                    asChild\r\n                  >\r\n                    <Link href=\"/class/login\" onClick={toggleMenu}>\r\n                      Tutor Login\r\n                    </Link>\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    className=\"w-full text-[#ff914d] hover:bg-gray-900 rounded-lg py-3 border border-gray-700\"\r\n                    asChild\r\n                  >\r\n                    <Link href=\"/student/login\" onClick={toggleMenu}>\r\n                      Student Login\r\n                    </Link>\r\n                  </Button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {isStudentLoggedIn && <ProfileCompletionIndicator />}\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAvCA;;;;;;;;;;;;;;;;;;;;;;;;AAyCA,MAAM,SAAS;IACb,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAC1C,CAAC,QAAqB,MAAM,IAAI;IAElC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,WAAW,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,IAAI,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IACzB,MAAM,QAAQ,eAAe;IAE7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,CAAA,GAAA,mHAAA,CAAA,yBAAsB,AAAD;QACxC,qBAAqB;QAErB,IAAI,YAAY;YACd,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,YAAY;gBACd,eAAe,KAAK,KAAK,CAAC;YAC5B;YACA,SAAS,CAAA,GAAA,8IAAA,CAAA,sBAAmB,AAAD;QAC7B;QACA,MAAM,mBAAmB;YACvB,IAAI,mBAAmB,MAAM,IAAI;gBAC/B,IAAI;oBACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,KAAK,EAAE,EAAE;oBACtE,IAAI,SAAS,IAAI,IAAI,SAAS,IAAI,CAAC,MAAM,EAAE;wBACzC,eAAe,SAAS,IAAI,CAAC,MAAM,CAAC,MAAM;oBAC5C;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAChD;YACF;QACF;QAEA;QAEA,MAAM,sBAAsB;YAC1B,MAAM,iBAAiB,CAAA,GAAA,mHAAA,CAAA,yBAAsB,AAAD;YAC5C,qBAAqB;YACrB,IAAI,gBAAgB;gBAClB,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,IAAI,YAAY;oBACd,eAAe,KAAK,KAAK,CAAC;gBAC5B;gBACA,SAAS,CAAA,GAAA,8IAAA,CAAA,sBAAmB,AAAD;YAC7B,OAAO;gBACL,eAAe;YACjB;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QAEnC,IAAI,WAAW,OAAO,EAAE;YACtB,MAAM,QAAQ,WAAW,OAAO,CAAC,qBAAqB,GAAG,KAAK;YAC9D,gBAAgB;QAClB;QAEA,OAAO;YACL,OAAO,mBAAmB,CAAC,WAAW;QACxC;IACF,GAAG;QAAC;KAAS;IAEb,CAAA,GAAA,qLAAA,CAAA,oBAAiB,AAAD,EAAE,CAAC,MAAM;QACvB,IAAI,iBAAiB,GAAG;QACxB,MAAM,WAAW,EAAE,GAAG;QACtB,MAAM,SAAS,AAAC,QAAQ,QAAS;QACjC,IAAI,OAAO,WAAW;QACtB,IAAI,QAAQ,CAAC,cAAc;YACzB,OAAO;QACT;QACA,EAAE,GAAG,CAAC;IACR;IAEA,MAAM,aAAa,IAAM,cAAc,CAAC;IAExC,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD;YACnC,IAAI,SAAS,OAAO,KAAK,OAAO;gBAC9B,CAAA,GAAA,mHAAA,CAAA,wBAAqB,AAAD;gBACpB,qBAAqB;gBACrB,eAAe;gBACf,aAAa,UAAU,CAAC;gBACxB,SAAS,CAAA,GAAA,6IAAA,CAAA,0BAAuB,AAAD;gBAC/B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,aAAa,CAAC,IAAI,MAAM;YACjC,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,OAAO,IAAI;gBAChC,CAAA,GAAA,mHAAA,CAAA,wBAAqB,AAAD;gBACpB,qBAAqB;gBACrB,eAAe;gBACf,aAAa,UAAU,CAAC;gBACxB,SAAS,CAAA,GAAA,6IAAA,CAAA,0BAAuB,AAAD;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,oBAAoB;YAChC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,aAAa,UAAU,CAAC;YACxB,CAAA,GAAA,mHAAA,CAAA,wBAAqB,AAAD;YACpB,qBAAqB;YACrB,eAAe;YACf,SAAS,CAAA,GAAA,6IAAA,CAAA,0BAAuB,AAAD;QACjC;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD,EAAE,MAAM,WAAW,MAAM;YAE1D,IAAI,SAAS,OAAO,EAAE;gBACpB,MAAM,EAAE,KAAK,EAAE,GAAG,SAAS,IAAI;gBAC/B,MAAM,cAAc,6DAAwC,sBAAsB,EAAE,MAAM,GAAG,OAAO,EAAE,OAAO;gBAC7G,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,OAAO,IAAI;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,WAAW;QACf;YAAE,MAAM;YAAqB,OAAO;QAAa;QACjD;YAAE,MAAM;YAAU,OAAO;QAAW;QACpC;YACE,MAAM;YACN,qBACE,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;kCAAK;;;;;;oBACL,mCAAqB,8OAAC,8IAAA,CAAA,UAAa;wBAAC,WAAW,aAAa;;;;;;;;;;;;YAGjE,OAAO;QACT;QACA;YAAE,MAAM;YAAY,OAAO;QAAS;QACpC;YAAE,MAAM;YAAU,OAAO;QAAQ;KAClC;IAED,MAAM,iBAAiB;QACrB;YACE,MAAM;YACN,oBAAM,8OAAC,kNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,OAAO;QACT;QACA;YACE,MAAM;YACN,oBAAM,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;YAC/B,OAAO;QACT;QACA;YACE,MAAM;YACN,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;QACT;QACA;YACE,MAAM;YACN,oBAAM,8OAAC,oNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YAC7B,OAAO;QACT;QACA;YACE,SAAS;YACT,oBAAM,8OAAC,4NAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,OAAO;QACT;QACA;YACE,MAAM;YACN,oBAAM,8OAAC,0MAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;QACT;WACI,gBAAgB,aAAa;YAAC;gBAChC,MAAM;gBACN,oBAAM,8OAAC,gNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;gBAC3B,OAAO;YACT;SAAE,GAAG,EAAE;KACR;IAED,qBACE;;0BACE,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CAEV,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;;;;;;0CAId,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;;4CAET,KAAK,KAAK;4CACV,KAAK,KAAK,kBACT,8OAAC;gDAAK,WAAU;0DAA+E;;;;;;;uCAN5F,KAAK,IAAI;;;;;;;;;;0CAcpB,8OAAC;gCAAI,WAAU;;oCACZ,mBAAmB,kCAClB;;0DACE,8OAAC,6IAAA,CAAA,UAAgB;gDACf,UAAU,kBAAkB,UAAU;;;;;;0DAExC,8OAAC,mIAAA,CAAA,UAAO;;kEACN,8OAAC,mIAAA,CAAA,iBAAc;wDAAC,OAAO;kEACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DAAC,WAAU;sEAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;gEAAC,WAAU;0EACvB,kBACG,MAAM,aAAa,MAAM,WACvB,GAAG,KAAK,SAAS,CAAC,EAAE,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACrD,OACF,aAAa,aAAa,aAAa,WACvC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACnE;;;;;;;;;;;;;;;;kEAIV,8OAAC,mIAAA,CAAA,iBAAc;wDAAC,WAAU;;0EACxB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kIAAA,CAAA,SAAM;wEAAC,WAAU;kFAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;4EAAC,WAAU;sFACvB,kBACG,MAAM,aAAa,MAAM,WACvB,GAAG,KAAK,SAAS,CAAC,EAAE,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACrD,OACF,aAAa,aAAa,aAAa,WACvC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACnE;;;;;;;;;;;kFAGR,8OAAC;;0FACC,8OAAC;gFAAE,WAAU;0FACV,kBACG,MAAM,aAAa,MAAM,WACvB,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,GACpC,MAAM,aAAa,kBACrB,aAAa,aAAa,aAAa,WACvC,GAAG,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,EAAE,GAClD;;;;;;0FAEN,8OAAC;gFAAE,WAAU;0FACV,kBACG,MAAM,aAAa,sBACnB,aAAa,aAAa;;;;;;;;;;;;;;;;;;0EAKpC,8OAAC;gEAAI,WAAU;0EACZ,gCACC;;wEACG,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,kIAAA,CAAA,SAAM;gFACL,OAAO;gFACP,SAAQ;gFACR,WAAU;0FAGT,KAAK,IAAI,iBACR,8OAAC,4JAAA,CAAA,UAAI;oFACH,MAAM,KAAK,IAAI;oFACf,WAAU;;wFAET,KAAK,IAAI;sGACV,8OAAC;sGAAM,KAAK,KAAK;;;;;;;;;;;yGAGnB,8OAAC;oFACC,SAAS,KAAK,OAAO;oFACrB,WAAU;;wFAET,KAAK,IAAI;sGACV,8OAAC;sGAAM,KAAK,KAAK;;;;;;;;;;;;+EAhBhB,KAAK,IAAI,IAAI,KAAK,KAAK;;;;;sFAqBhC,8OAAC,kIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,WAAU;4EACV,SAAS;gFACP,IAAI;oFACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CACvC,uBACA,CAAC;oFAEH,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;wFACzB,OAAO,IAAI,CAAC;wFACZ,SAAS,CAAA,GAAA,mIAAA,CAAA,YAAS,AAAD;wFACjB,aAAa,UAAU,CAAC;wFACxB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oFAChB;gFACF,EAAE,OAAO,OAAO;oFACd,QAAQ,KAAK,CAAC,iBAAiB;oFAC/B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gFACd;4EACF;;8FAEA,8OAAC,kMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;8FAChB,8OAAC;8FAAK;;;;;;;;;;;;;iGAIV;8EACE,cAAA,8OAAC;wEAAI,WAAU;;4EACZ;gFACC;oFACE,MAAM;oFACN,oBAAM,8OAAC,kNAAA,CAAA,aAAU;wFAAC,WAAU;;;;;;oFAC5B,OAAO;gFACT;gFACA;oFACE,MAAM;oFACN,oBACE,8OAAC,wNAAA,CAAA,gBAAa;wFAAC,WAAU;;;;;;oFAE3B,OAAO;gFACT;gFACA;oFACE,MAAM;oFACN,oBAAM,8OAAC,oMAAA,CAAA,QAAK;wFAAC,WAAU;;;;;;oFACvB,OAAO;gFACT;gFACA;oFACE,MAAM;oFACN,oBACE,8OAAC,oNAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;oFAEzB,OAAO;gFACT;gFACA;oFACE,MAAM;oFACN,oBAAM,8OAAC,0MAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;oFACxB,OAAO;gFACT;gFACA;oFACE,MAAM;oFACN,oBACE,8OAAC,oNAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;oFAEzB,OAAO;gFACT;6EACD,CAAC,GAAG,CAAC,CAAC,qBACL,8OAAC,kIAAA,CAAA,SAAM;oFACL,OAAO;oFACP,SAAQ;oFACR,WAAU;8FAGV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wFACH,MAAM,KAAK,IAAI;wFACf,WAAU;;4FAET,KAAK,IAAI;0GACV,8OAAC;0GAAM,KAAK,KAAK;;;;;;;;;;;;mFAPd,KAAK,IAAI;;;;;0FAWlB,8OAAC,kIAAA,CAAA,SAAM;gFACL,SAAS;gFACT,WAAU;;kGAEV,8OAAC,kMAAA,CAAA,OAAI;wFAAC,WAAU;;;;;;kGAChB,8OAAC;kGAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;qEAUtB;kDACE,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,WAAU;oDACV,OAAO;8DAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;kEAAe;;;;;;;;;;;8DAG5B,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,OAAO;8DAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;kEAAiB;;;;;;;;;;;;;;;;;;kDAMpC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;kDAER,2BACC,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;iEAEb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5B,8OAAC;;kCACC,8OAAC;wBACC,WAAW,CAAC,uGAAuG,EACjH,aAAa,kBAAkB,oBAC/B;kCAEF,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;sDAEZ,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS;sDAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;gCAIhB,CAAC,mBAAmB,iBAAiB,mBACpC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;0DAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;oDAAC,WAAU;8DACvB,kBACG,MAAM,aAAa,MAAM,WACvB,GAAG,KAAK,SAAS,CAAC,EAAE,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACrD,OACF,aAAa,aAAa,aAAa,WACvC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACnE;;;;;;;;;;;0DAGR,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEACV,kBACG,MAAM,aAAa,MAAM,WACvB,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,GACpC,MAAM,aAAa,kBACrB,aAAa,aAAa,aAAa,WACvC,GAAG,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,EAAE,GAClD;;;;;;kEAEN,8OAAC;wDAAE,WAAU;kEACV,kBACG,MAAM,aAAa,sBACnB,aAAa,aAAa;;;;;;;;;;;;;;;;;;;;;;;8CAOxC,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,SAAS;;8DAET,8OAAC;oDAAI,WAAU;8DACZ,OAAO,KAAK,KAAK,KAAK,yBACrB,8OAAC;kEAAM,KAAK,KAAK;;;;;+DAEjB,KAAK,KAAK;;;;;;gDAGb,KAAK,KAAK,kBACT,8OAAC;oDAAK,WAAU;8DAA4D;;;;;;;2CAbzE,KAAK,IAAI;;;;;;;;;;8CAqBpB,8OAAC;oCAAI,WAAU;;wCACZ,iCACC;;gDACG,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,kIAAA,CAAA,SAAM;wDACL,OAAO;wDACP,SAAQ;wDACR,WAAU;kEAGT,KAAK,IAAI,iBACR,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,KAAK,IAAI;4DACf,WAAU;4DACV,SAAS;;gEAER,KAAK,IAAI;8EACV,8OAAC;8EAAM,KAAK,KAAK;;;;;;;;;;;iFAGnB,8OAAC;4DACC,SAAS;gEACP;4DACF;4DACA,WAAU;;gEAET,KAAK,IAAI;8EACV,8OAAC;8EAAM,KAAK,KAAK;;;;;;;;;;;;uDAnBhB,KAAK,IAAI,IAAI,KAAK,KAAK;;;;;8DAwBhC,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS;wDACP,IAAI;4DACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CACvC,uBACA,CAAC;4DAEH,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gEACzB,OAAO,IAAI,CAAC;gEACZ,SAAS,CAAA,GAAA,mIAAA,CAAA,YAAS,AAAD;gEACjB,aAAa,UAAU,CAAC;gEACxB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;4DAChB;wDACF,EAAE,OAAO,OAAO;4DACd,QAAQ,KAAK,CAAC,iBAAiB;4DAC/B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wDACd;wDACA;oDACF;;sEAEA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;sEAAK;;;;;;;;;;;;;;wCAKX,mCACC;;gDACG;oDACC;wDACE,MAAM;wDACN,oBAAM,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;wDAC5B,OAAO;oDACT;oDACA;wDACE,MAAM;wDACN,oBAAM,8OAAC,wNAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;wDAC/B,OAAO;oDACT;oDACA;wDACE,MAAM;wDACN,oBAAM,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDACvB,OAAO;oDACT;oDACA;wDACE,MAAM;wDACN,oBAAM,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDAC7B,OAAO;oDACT;oDACA;wDACE,MAAM;wDACN,oBAAM,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDACxB,OAAO;oDACT;oDACA;wDACE,MAAM;wDACN,oBAAM,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDAC7B,OAAO;oDACT;iDACD,CAAC,GAAG,CAAC,CAAC,qBACL,8OAAC,kIAAA,CAAA,SAAM;wDACL,OAAO;wDACP,SAAQ;wDACR,WAAU;kEAGV,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,KAAK,IAAI;4DACf,WAAU;4DACV,SAAS;;gEAER,KAAK,IAAI;8EACV,8OAAC;8EAAM,KAAK,KAAK;;;;;;;;;;;;uDARd,KAAK,IAAI;;;;;8DAYlB,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS;wDACP;wDACA;oDACF;;sEAEA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;sEAAK;;;;;;;;;;;;;;wCAKX,CAAC,mBAAmB,CAAC,mCACpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,WAAU;oDACV,OAAO;8DAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAe,SAAS;kEAAY;;;;;;;;;;;8DAIjD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,OAAO;8DAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAiB,SAAS;kEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAU5D,mCAAqB,8OAAC,uJAAA,CAAA,UAA0B;;;;;;;;;;;;;AAIzD;uCAEe", "debugId": null}}, {"offset": {"line": 2378, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/public/exam-logo.jpg.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 3192, height: 2504, blurDataURL: \"data:image/jpeg;base64,/9j/4AAQSkZJRgABAgAAAQABAAD/wAARCAAGAAgDAREAAhEBAxEB/9sAQwAKBwcIBwYKCAgICwoKCw4YEA4NDQ4dFRYRGCMfJSQiHyIhJis3LyYpNCkhIjBBMTQ5Oz4+PiUuRElDPEg3PT47/9sAQwEKCwsODQ4cEBAcOygiKDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwDyt9PkS1UlwcqTStqO+h//2Q==\", blurWidth: 8, blurHeight: 6 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,+GAAA,CAAA,UAAG;IAAE,OAAO;IAAM,QAAQ;IAAM,aAAa;IAAm3B,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 2397, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn('leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return <div data-slot=\"card-content\" className={cn('px-6', className)} {...props} />;\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kEAAkE;QAC/E,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBAAO,8OAAC;QAAI,aAAU;QAAe,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAa,GAAG,KAAK;;;;;;AAClF;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2494, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/examApi.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\nimport { ExamInput } from '@/lib/types';\r\n\r\nexport const getExams = async (page: number = 1, limit: number = 10, applicantId?: string) => {\r\n  try {\r\n    const response = await axiosInstance.get(`/exams?page=${page}&limit=${limit}${applicantId ? `&applicantId=${applicantId}` : ''}`, {\r\n      headers: {\r\n        'Server-Select': 'uwhizServer',\r\n      },\r\n    });\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to fetch Exam: ${error.response?.data?.message || error.message}`,\r\n    };\r\n  }\r\n};\r\n\r\nexport const getExamsById = async (examId: number, applicantId?: string) => {\r\n  try {\r\n    const response = await axiosInstance.get(\r\n      `/exams/${examId}${applicantId ? `?applicantId=${applicantId}` : ''}`,\r\n      {\r\n        headers: {\r\n          \"Server-Select\": \"uwhizServer\",\r\n        },\r\n      }\r\n    );\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to fetch Exam: ${error.response?.data?.message || error.message}`,\r\n    };\r\n  }\r\n};\r\n\r\nexport const createExam = async (data: ExamInput) => {\r\n  try {\r\n    const response = await axiosInstance.post('/exams', data,{\r\n       headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to create exam: ${error.response?.data?.message || error.message}`,\r\n    };\r\n  }\r\n};\r\n\r\nexport const updateExam = async (id: number, data: Partial<ExamInput>) => {\r\n  try {\r\n    const response = await axiosInstance.put(`/exams/${id}`, data,{\r\n       headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to update Exam: ${error.response?.data?.message || error.message}`,\r\n    };\r\n  }\r\n};\r\n\r\nexport const deleteExam = async (id: number) => {\r\n  try {\r\n    const response = await axiosInstance.delete(`/exams/${id}`,{\r\n       headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to delete Exam: ${error.response?.data?.message || error.message}`,\r\n    };\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAGO,MAAM,WAAW,OAAO,OAAe,CAAC,EAAE,QAAgB,EAAE,EAAE;IACnE,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,KAAK,OAAO,EAAE,QAAQ,cAAc,CAAC,aAAa,EAAE,aAAa,GAAG,IAAI,EAAE;YAChI,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,sBAAsB,EAAE,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAAE;QAClF;IACF;AACF;AAEO,MAAM,eAAe,OAAO,QAAgB;IACjD,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CACtC,CAAC,OAAO,EAAE,SAAS,cAAc,CAAC,aAAa,EAAE,aAAa,GAAG,IAAI,EACrE;YACE,SAAS;gBACP,iBAAiB;YACnB;QACF;QAEF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,sBAAsB,EAAE,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAAE;QAClF;IACF;AACF;AAEO,MAAM,aAAa,OAAO;IAC/B,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,UAAU,MAAK;YACtD,SAAS;gBACR,iBAAiB;YACnB;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,uBAAuB,EAAE,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAAE;QACnF;IACF;AACF;AAEO,MAAM,aAAa,OAAO,IAAY;IAC3C,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE,MAAK;YAC3D,SAAS;gBACR,iBAAiB;YACnB;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,uBAAuB,EAAE,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAAE;QACnF;IACF;AACF;AAEO,MAAM,aAAa,OAAO;IAC/B,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,IAAI,EAAC;YACxD,SAAS;gBACR,iBAAiB;YACnB;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,uBAAuB,EAAE,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAAE;QACnF;IACF;AACF", "debugId": null}}, {"offset": {"line": 2584, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/examApplicantEmailApi.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\n\r\nexport const sendExamApplicantEmail = async (\r\n  examId: number,\r\n  exam_name: string,\r\n  email: string\r\n): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.post(\r\n      '/examApplicantEmail/send-exam-applicant-email',\r\n      {\r\n        examId,\r\n        exam_name,\r\n        email,\r\n      }\r\n    );\r\n    return response.data;\r\n  } catch (error: any) {\r\n    throw new Error(error.response?.data?.message || 'Failed to send exam applicant email');\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,yBAAyB,OACpC,QACA,WACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CACvC,iDACA;YACE;YACA;YACA;QACF;QAEF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MAAM,MAAM,QAAQ,EAAE,MAAM,WAAW;IACnD;AACF", "debugId": null}}, {"offset": {"line": 2607, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/examApplicationApi.ts"], "sourcesContent": ["import { axiosInstance } from \"@/lib/axios\";\r\n\r\n// Apply for an exam\r\nexport const applyForExam = async (\r\n  examId: number,\r\n  applicantId: string\r\n): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.post(\r\n      \"/examApplication\",\r\n      {\r\n        examId,\r\n        applicantId,\r\n      },\r\n      {\r\n        headers: {\r\n          \"Server-Select\": \"uwhizServer\",\r\n        },\r\n      }\r\n    );\r\n    return response.data;\r\n  } catch (error: any) {\r\n    throw new Error(error.response?.data?.error || \"Failed to apply for exam\");\r\n  }\r\n};\r\n\r\n// Get all exam applications (for admin)\r\nexport const getAllExamApplications = async (\r\n  page: number = 1,\r\n  limit: number = 10,\r\n  examId?: number\r\n): Promise<any> => {\r\n  try {\r\n    const params: any = { page, limit };\r\n    if (examId !== undefined) {\r\n      params.examId = examId;\r\n    }\r\n    const response = await axiosInstance.get(`/examApplication/${examId}`, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n      params,\r\n    },);\r\n    return response.data;\r\n  } catch (error: any) {\r\n    throw new Error(\r\n      error.response?.data?.error || \"Failed to fetch exam applications\"\r\n    );\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;;AAGO,MAAM,eAAe,OAC1B,QACA;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CACvC,oBACA;YACE;YACA;QACF,GACA;YACE,SAAS;gBACP,iBAAiB;YACnB;QACF;QAEF,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MAAM,MAAM,QAAQ,EAAE,MAAM,SAAS;IACjD;AACF;AAGO,MAAM,yBAAyB,OACpC,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB;IAEA,IAAI;QACF,MAAM,SAAc;YAAE;YAAM;QAAM;QAClC,IAAI,WAAW,WAAW;YACxB,OAAO,MAAM,GAAG;QAClB;QACA,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,QAAQ,EAAE;YACrE,SAAS;gBACP,iBAAiB;YACnB;YACA;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MACR,MAAM,QAAQ,EAAE,MAAM,SAAS;IAEnC;AACF", "debugId": null}}, {"offset": {"line": 2654, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/uwhizPreventReattemptApi.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\n\r\nexport const uwhizPreventReattempApi = async (studentId: string,examId:number) => {\r\n  try {\r\n    const response = await axiosInstance.get(`check-attempt?studentId=${studentId}&examId=${examId}`,{\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed To Get Student And Exam Detail: ${error.response?.data?.message || error.message}`,\r\n    };\r\n  }\r\n};"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,0BAA0B,OAAO,WAAkB;IAC9D,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,wBAAwB,EAAE,UAAU,QAAQ,EAAE,QAAQ,EAAC;YAC/F,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,uCAAuC,EAAE,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAAE;QACnG;IACF;AACF", "debugId": null}}, {"offset": {"line": 2680, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/Footer.tsx"], "sourcesContent": ["'use client';\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport {\r\n  FaFacebookF,\r\n  FaTwitter,\r\n  FaInstagram,\r\n  FaLinkedinIn,\r\n  FaTumblr,\r\n  FaPinterestP,\r\n  FaEnvelope,\r\n} from 'react-icons/fa';\r\n\r\nconst Footer = () => {\r\n  return (\r\n    <footer className=\"bg-black text-gray-300 px-6 py-16\">\r\n      <div className=\"container mx-auto max-w-7xl space-y-16\">\r\n        <div className=\"flex flex-col md:flex-row items-center justify-between gap-6\">\r\n          <Link href=\"/\" className=\"flex items-center gap-2\">\r\n            <Image\r\n              src=\"/logo_black.png\"\r\n              alt=\"Logo\"\r\n              width={200}\r\n              height={40}\r\n              className=\"object-contain\"\r\n            />\r\n          </Link>\r\n\r\n          <div className=\"flex flex-wrap justify-center gap-1\">\r\n            {[\r\n              { href: 'mailto:<EMAIL>', icon: FaEnvelope, label: 'Email Us' },\r\n              {\r\n                href: 'https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09',\r\n                icon: FaTwitter,\r\n                label: 'Twitter',\r\n              },\r\n              {\r\n                href: 'https://www.facebook.com/share/1FNYcyqawH/',\r\n                icon: FaFacebookF,\r\n                label: 'Facebook',\r\n              },\r\n              {\r\n                href: 'https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==',\r\n                icon: FaInstagram,\r\n                label: 'Instagram',\r\n              },\r\n              {\r\n                href: 'https://www.linkedin.com/company/uest-edtech/',\r\n                icon: FaLinkedinIn,\r\n                label: 'LinkedIn',\r\n              },\r\n              { href: 'https://pin.it/1Di0EFtAa', icon: FaPinterestP, label: 'Pinterest' },\r\n              {\r\n                href: 'https://www.tumblr.com/uestedtech?source=share',\r\n                icon: FaTumblr,\r\n                label: 'Tumblr',\r\n              },\r\n            ].map(({ href, icon: Icon, label }) => (\r\n              <div key={label} className=\"flex flex-col items-center\">\r\n                <Link\r\n                  href={href}\r\n                  className=\"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition\"\r\n                  title={label}\r\n                >\r\n                  <Icon className=\"text-xl text-white hover:text-gray-400 transition\" />\r\n                </Link>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10\">\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">About</h3>\r\n            <ul className=\"space-y-2 text-sm\">\r\n              <li>\r\n                <Link href=\"/verified-classes\" className=\"hover:text-white transition\">\r\n                  Find Tutors\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/support\" className=\"hover:text-white transition\">\r\n                  Support\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/careers\" className=\"hover:text-white transition\">\r\n                  Careers\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">For Students</h3>\r\n            <ul className=\"space-y-2 text-sm\">\r\n              <li>\r\n                <Link href=\"/student/login\" className=\"hover:text-white transition\">\r\n                  Student Login\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/verified-classes\" className=\"hover:text-white transition\">\r\n                  Find Online Tutor\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/uwhiz\" className=\"hover:text-white transition\">\r\n                  Uwhiz\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">Contact</h3>\r\n            <address className=\"not-italic text-sm space-y-1 leading-relaxed\">\r\n              <p>Head Office</p>\r\n              <p>4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641</p>\r\n              <p>Contact: +91 96 877 877 88</p>\r\n              <p>Email: <EMAIL></p>\r\n            </address>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">Apps</h3>\r\n           <Link href=\"https://play.google.com/store/apps/details?id=com.uest\" target=\"_blank\">\r\n            <Image\r\n              src=\"/playstore.png\"\r\n              alt=\"Google Play Store\"\r\n              width={180}\r\n              height={50}\r\n              className=\"object-contain\"\r\n            />\r\n          </Link>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Bottom Bar */}\r\n        <div className=\"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4\">\r\n          <p>© 2025 uest.in. All rights reserved.</p>\r\n          <div className=\"flex gap-4\">\r\n            <Link href=\"/terms-and-conditions\" className=\"hover:text-white transition\">\r\n              Terms & Conditions\r\n            </Link>\r\n            <Link href=\"/privacy-policy\" className=\"hover:text-white transition\">\r\n              Privacy Policy\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAcA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;sCAId,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,MAAM;oCAA8B,MAAM,8IAAA,CAAA,aAAU;oCAAE,OAAO;gCAAW;gCAC1E;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,YAAS;oCACf,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,cAAW;oCACjB,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,cAAW;oCACjB,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,eAAY;oCAClB,OAAO;gCACT;gCACA;oCAAE,MAAM;oCAA4B,MAAM,8IAAA,CAAA,eAAY;oCAAE,OAAO;gCAAY;gCAC3E;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,WAAQ;oCACd,OAAO;gCACT;6BACD,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,iBAChC,8OAAC;oCAAgB,WAAU;8CACzB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM;wCACN,WAAU;wCACV,OAAO;kDAEP,cAAA,8OAAC;4CAAK,WAAU;;;;;;;;;;;mCANV;;;;;;;;;;;;;;;;8BAahB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAA8B;;;;;;;;;;;sDAIzE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA8B;;;;;;;;;;;sDAIhE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;sCAOpE,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAiB,WAAU;0DAA8B;;;;;;;;;;;sDAItE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAA8B;;;;;;;;;;;sDAIzE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;sCAOlE,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAQ,WAAU;;sDACjB,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;sCAIP,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACvD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAyD,QAAO;8CAC1E,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAOhB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCAAE;;;;;;sCACH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAwB,WAAU;8CAA8B;;;;;;8CAG3E,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAkB,WAAU;8CAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjF;uCAEe", "debugId": null}}, {"offset": {"line": 3091, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/progress.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as ProgressPrimitive from '@radix-ui/react-progress';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Progress({\r\n  className,\r\n  value,\r\n  ...props\r\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\r\n  return (\r\n    <ProgressPrimitive.Root\r\n      data-slot=\"progress\"\r\n      className={cn('bg-primary/20 relative h-2 w-full overflow-hidden rounded-full', className)}\r\n      {...props}\r\n    >\r\n      <ProgressPrimitive.Indicator\r\n        data-slot=\"progress-indicator\"\r\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\r\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\r\n      />\r\n    </ProgressPrimitive.Root>\r\n  );\r\n}\r\n\r\nexport { Progress };\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,8OAAC,oKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kEAAkE;QAC/E,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE", "debugId": null}}, {"offset": {"line": 3130, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/uwhiz/countDownTimer.tsx"], "sourcesContent": ["'use client';\r\nimport { useState, useEffect, memo } from 'react';\r\nimport { Exam } from '@/lib/types';\r\nimport { RiTimerFlashFill } from 'react-icons/ri';\r\nimport { differenceInSeconds, addMinutes } from 'date-fns';\r\nimport { toZonedTime } from 'date-fns-tz';\r\n\r\ninterface CountdownTimerProps {\r\n  exam: Exam;\r\n}\r\n\r\nconst CountdownTimer = ({ exam }: CountdownTimerProps) => {\r\n  const [timerState, setTimerState] = useState<'registration' | 'application' | 'exam' | 'expired' | 'late'>('registration');\r\n\r\n  const [countdown, setCountdown] = useState<{\r\n    days: number;\r\n    hours: number;\r\n    minutes: number;\r\n    seconds: number;\r\n  }>({ days: 0, hours: 0, minutes: 0, seconds: 0 });\r\n\r\n  const [examTimer, setExamTimer] = useState<{\r\n    minutes: number;\r\n    seconds: number;\r\n    isLate: boolean;\r\n  }>({\r\n    minutes: 10,\r\n    seconds: 0,\r\n    isLate: false,\r\n  });\r\n\r\n  useEffect(() => {\r\n    const calculateStatus = () => {\r\n      const startRegistrationDate = exam.start_registration_date\r\n        ? new Date(exam.start_registration_date)\r\n        : null;\r\n      const startDate = new Date(exam.start_date);\r\n\r\n      if (isNaN(startDate.getTime())) {\r\n        console.error(`Invalid start_date for exam ${exam.id}: ${exam.start_date}`);\r\n        setTimerState('expired');\r\n        setCountdown({ days: 0, hours: 0, minutes: 0, seconds: 0 });\r\n        setExamTimer({ minutes: 0, seconds: 0, isLate: true });\r\n        return;\r\n      }\r\n\r\n      const now = toZonedTime(new Date(), 'Asia/Kolkata');\r\n      const istStartDate = toZonedTime(startDate, 'Asia/Kolkata');\r\n      const istStartTime = istStartDate.getTime();\r\n      const startWindowEnd = addMinutes(istStartDate, exam.duration).getTime(); \r\n      const nowTime = now.getTime();\r\n\r\n      // 1: Registration Phase\r\n      if (\r\n        startRegistrationDate &&\r\n        !isNaN(startRegistrationDate.getTime()) &&\r\n        nowTime < startRegistrationDate.getTime()\r\n      ) {\r\n        const diffSeconds = differenceInSeconds(startRegistrationDate, now);\r\n        const days = Math.floor(diffSeconds / (60 * 60 * 24));\r\n        const hours = Math.floor((diffSeconds % (60 * 60 * 24)) / (60 * 60));\r\n        const minutes = Math.floor((diffSeconds % (60 * 60)) / 60);\r\n        const seconds = diffSeconds % 60;\r\n        setTimerState('registration');\r\n        setCountdown({ days, hours, minutes, seconds });\r\n        setExamTimer({ minutes: 10, seconds: 0, isLate: false });\r\n      }\r\n      // 2: Application Phase\r\n      else if (nowTime < istStartTime) {\r\n        const diffSeconds = differenceInSeconds(istStartDate, now);\r\n        const days = Math.floor(diffSeconds / (60 * 60 * 24));\r\n        const hours = Math.floor((diffSeconds % (60 * 60 * 24)) / (60 * 60));\r\n        const minutes = Math.floor((diffSeconds % (60 * 60)) / 60);\r\n        const seconds = diffSeconds % 60;\r\n        setTimerState('application');\r\n        setCountdown({ days, hours, minutes, seconds });\r\n        setExamTimer({ minutes: 10, seconds: 0, isLate: false });\r\n      }\r\n      //  3: Exam Start \r\n      else if (nowTime >= istStartTime && nowTime <= startWindowEnd) {\r\n        const diffSeconds = differenceInSeconds(new Date(startWindowEnd), now);\r\n        const minutes = Math.floor(diffSeconds / 60);\r\n        const seconds = diffSeconds % 60;\r\n        setTimerState('exam');\r\n        setCountdown({ days: 0, hours: 0, minutes: 0, seconds: 0 });\r\n        setExamTimer({ minutes, seconds, isLate: false });\r\n      }\r\n      // 4: Exam Late/Finished\r\n      else {\r\n        setTimerState('late');\r\n        setCountdown({ days: 0, hours: 0, minutes: 0, seconds: 0 });\r\n        setExamTimer({ minutes: 0, seconds: 0, isLate: true });\r\n      }\r\n    };\r\n\r\n    calculateStatus();\r\n    const interval = setInterval(calculateStatus, 1000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [exam.start_date, exam.start_registration_date, exam.id]);\r\n\r\n  return (\r\n    <div className=\"flex items-center justify-center w-full gap-2 border-2 px-4 bg-orange-50 group-hover:bg-orange-100 transition-colors duration-300\">\r\n      <RiTimerFlashFill className=\"text-2xl text-customOrange animate-pulse\" />\r\n      <span className=\"font-semibold text-customOrange text-sm\">\r\n        {timerState === 'registration' ? (\r\n          <span>\r\n            Registration Starts in: {countdown.days}d {countdown.hours}h {countdown.minutes}m{' '}\r\n            {countdown.seconds}s\r\n          </span>\r\n        ) : timerState === 'application' ? (\r\n          <span>\r\n            Exam Starts in: {countdown.days}d {countdown.hours}h {countdown.minutes}m{' '}\r\n            {countdown.seconds}s\r\n          </span>\r\n        ) : timerState === 'exam' ? (\r\n          <span>\r\n            You May Starts In: {examTimer.minutes}m {examTimer.seconds}s\r\n          </span>\r\n        ) : timerState === 'late' ? (\r\n          <span>You Are Late</span>\r\n        ) : (\r\n          <span>Expired</span>\r\n        )}\r\n      </span>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default memo(CountdownTimer);"], "names": [], "mappings": ";;;;AACA;AAEA;AACA;AAAA;AACA;AAAA;AALA;;;;;;AAWA,MAAM,iBAAiB,CAAC,EAAE,IAAI,EAAuB;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgE;IAE3G,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAKtC;QAAE,MAAM;QAAG,OAAO;QAAG,SAAS;QAAG,SAAS;IAAE;IAE/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAItC;QACD,SAAS;QACT,SAAS;QACT,QAAQ;IACV;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,MAAM,wBAAwB,KAAK,uBAAuB,GACtD,IAAI,KAAK,KAAK,uBAAuB,IACrC;YACJ,MAAM,YAAY,IAAI,KAAK,KAAK,UAAU;YAE1C,IAAI,MAAM,UAAU,OAAO,KAAK;gBAC9B,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,UAAU,EAAE;gBAC1E,cAAc;gBACd,aAAa;oBAAE,MAAM;oBAAG,OAAO;oBAAG,SAAS;oBAAG,SAAS;gBAAE;gBACzD,aAAa;oBAAE,SAAS;oBAAG,SAAS;oBAAG,QAAQ;gBAAK;gBACpD;YACF;YAEA,MAAM,MAAM,CAAA,GAAA,wKAAA,CAAA,cAAW,AAAD,EAAE,IAAI,QAAQ;YACpC,MAAM,eAAe,CAAA,GAAA,wKAAA,CAAA,cAAW,AAAD,EAAE,WAAW;YAC5C,MAAM,eAAe,aAAa,OAAO;YACzC,MAAM,iBAAiB,CAAA,GAAA,yIAAA,CAAA,aAAU,AAAD,EAAE,cAAc,KAAK,QAAQ,EAAE,OAAO;YACtE,MAAM,UAAU,IAAI,OAAO;YAE3B,wBAAwB;YACxB,IACE,yBACA,CAAC,MAAM,sBAAsB,OAAO,OACpC,UAAU,sBAAsB,OAAO,IACvC;gBACA,MAAM,cAAc,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,uBAAuB;gBAC/D,MAAM,OAAO,KAAK,KAAK,CAAC,cAAc,CAAC,KAAK,KAAK,EAAE;gBACnD,MAAM,QAAQ,KAAK,KAAK,CAAC,AAAC,cAAc,CAAC,KAAK,KAAK,EAAE,IAAK,CAAC,KAAK,EAAE;gBAClE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,cAAc,CAAC,KAAK,EAAE,IAAK;gBACvD,MAAM,UAAU,cAAc;gBAC9B,cAAc;gBACd,aAAa;oBAAE;oBAAM;oBAAO;oBAAS;gBAAQ;gBAC7C,aAAa;oBAAE,SAAS;oBAAI,SAAS;oBAAG,QAAQ;gBAAM;YACxD,OAEK,IAAI,UAAU,cAAc;gBAC/B,MAAM,cAAc,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,cAAc;gBACtD,MAAM,OAAO,KAAK,KAAK,CAAC,cAAc,CAAC,KAAK,KAAK,EAAE;gBACnD,MAAM,QAAQ,KAAK,KAAK,CAAC,AAAC,cAAc,CAAC,KAAK,KAAK,EAAE,IAAK,CAAC,KAAK,EAAE;gBAClE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,cAAc,CAAC,KAAK,EAAE,IAAK;gBACvD,MAAM,UAAU,cAAc;gBAC9B,cAAc;gBACd,aAAa;oBAAE;oBAAM;oBAAO;oBAAS;gBAAQ;gBAC7C,aAAa;oBAAE,SAAS;oBAAI,SAAS;oBAAG,QAAQ;gBAAM;YACxD,OAEK,IAAI,WAAW,gBAAgB,WAAW,gBAAgB;gBAC7D,MAAM,cAAc,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,iBAAiB;gBAClE,MAAM,UAAU,KAAK,KAAK,CAAC,cAAc;gBACzC,MAAM,UAAU,cAAc;gBAC9B,cAAc;gBACd,aAAa;oBAAE,MAAM;oBAAG,OAAO;oBAAG,SAAS;oBAAG,SAAS;gBAAE;gBACzD,aAAa;oBAAE;oBAAS;oBAAS,QAAQ;gBAAM;YACjD,OAEK;gBACH,cAAc;gBACd,aAAa;oBAAE,MAAM;oBAAG,OAAO;oBAAG,SAAS;oBAAG,SAAS;gBAAE;gBACzD,aAAa;oBAAE,SAAS;oBAAG,SAAS;oBAAG,QAAQ;gBAAK;YACtD;QACF;QAEA;QACA,MAAM,WAAW,YAAY,iBAAiB;QAE9C,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC,KAAK,UAAU;QAAE,KAAK,uBAAuB;QAAE,KAAK,EAAE;KAAC;IAE3D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,8IAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;0BAC5B,8OAAC;gBAAK,WAAU;0BACb,eAAe,+BACd,8OAAC;;wBAAK;wBACqB,UAAU,IAAI;wBAAC;wBAAG,UAAU,KAAK;wBAAC;wBAAG,UAAU,OAAO;wBAAC;wBAAE;wBACjF,UAAU,OAAO;wBAAC;;;;;;2BAEnB,eAAe,8BACjB,8OAAC;;wBAAK;wBACa,UAAU,IAAI;wBAAC;wBAAG,UAAU,KAAK;wBAAC;wBAAG,UAAU,OAAO;wBAAC;wBAAE;wBACzE,UAAU,OAAO;wBAAC;;;;;;2BAEnB,eAAe,uBACjB,8OAAC;;wBAAK;wBACgB,UAAU,OAAO;wBAAC;wBAAG,UAAU,OAAO;wBAAC;;;;;;2BAE3D,eAAe,uBACjB,8OAAC;8BAAK;;;;;yCAEN,8OAAC;8BAAK;;;;;;;;;;;;;;;;;AAKhB;qDAEe,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 3350, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/uwhiz/examStatusButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Exam } from '@/lib/types';\r\nimport { toZonedTime } from 'date-fns-tz';\r\nimport { useSelector } from 'react-redux';\r\nimport { RootState } from '@/store';\r\nimport { toast } from 'sonner';\r\n\r\ninterface ExamStatusButtonProps {\r\n  exam: Exam;\r\n  hasApplied: boolean;\r\n  isMaxLimitReached: boolean;\r\n  hasAttempted: boolean;\r\n  onApplyClick: () => void;\r\n}\r\n\r\nconst ExamStatusButton = ({\r\n  exam,\r\n  hasApplied,\r\n  isMaxLimitReached,\r\n  hasAttempted,\r\n  onApplyClick,\r\n}: ExamStatusButtonProps) => {\r\n  const router = useRouter();\r\n  const isTutorLoggedIn = useSelector((state: RootState) => state.user.isAuthenticated); \r\n  const [examStatus, setExamStatus] = useState<'countdown' | 'start' | 'finished'>('countdown');\r\n  const [isStartWindowActive, setIsStartWindowActive] = useState(false);\r\n  const [isRegistrationOpen, setIsRegistrationOpen] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const calculateStatus = () => {\r\n      const startTime = new Date(exam.start_date).getTime();\r\n      const startRegistrationDate = exam.start_registration_date\r\n        ? new Date(exam.start_registration_date).getTime()\r\n        : null;\r\n\r\n      if (isNaN(startTime)) {\r\n        console.error(`Invalid start_date for exam ${exam.id}: ${exam.start_date}`);\r\n        setExamStatus('finished');\r\n        setIsStartWindowActive(false);\r\n        setIsRegistrationOpen(false);\r\n        return;\r\n      }\r\n\r\n      const now = toZonedTime(new Date(), 'Asia/Kolkata').getTime();\r\n      const startWindowEnd = startTime + exam.duration * 60 * 1000;\r\n\r\n      if (startRegistrationDate && now < startRegistrationDate) {\r\n        setIsRegistrationOpen(false);\r\n      } else {\r\n        setIsRegistrationOpen(true);\r\n      }\r\n\r\n      if (now < startTime) {\r\n        setExamStatus('countdown');\r\n        setIsStartWindowActive(false);\r\n      } else if (now >= startTime && now <= startWindowEnd) {\r\n        setExamStatus('start');\r\n        setIsStartWindowActive(true);\r\n      }  \r\n      else {\r\n        setExamStatus('finished');\r\n        setIsStartWindowActive(false);\r\n      }\r\n    };\r\n\r\n    calculateStatus();\r\n    const interval = setInterval(calculateStatus, 1000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [exam.start_date, exam.duration, exam.id, exam.start_registration_date]);\r\n\r\n  const handleStartExam = () => {\r\n    router.push(`/uwhiz-exam/${exam.id}`);\r\n  };\r\n\r\n  const handleViewResult = () => {\r\n    router.push(`/uwhiz-details/${exam.id}`);\r\n  };\r\n\r\n  const handleApplyClick = () => {\r\n    if (isTutorLoggedIn) {\r\n      toast.error('You are currently logged in as a tutor. Please log out and then log in as a student to apply for UWhiz.');\r\n      return;\r\n    }\r\n    onApplyClick();\r\n  };\r\n\r\n  if (hasAttempted) {\r\n    return (\r\n      <div className=\"flex flex-col items-center justify-center gap-4 mb-4 mx-5\">\r\n        <Button\r\n          className=\"w-full bg-gray-400 text-white font-semibold py-2 rounded-lg cursor-not-allowed\"\r\n          disabled\r\n        >\r\n          Attempted\r\n        </Button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex flex-col items-center justify-center gap-4 mb-4 mx-5\">\r\n      {examStatus === 'countdown' ? (\r\n        <Button\r\n          onClick={handleApplyClick}\r\n          className=\"w-full bg-customOrange hover:bg-[#E88143] text-white font-semibold py-2 rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl\"\r\n          disabled={isMaxLimitReached || hasApplied || !isRegistrationOpen}\r\n        >\r\n          {hasApplied\r\n            ? 'Applied'\r\n            : isMaxLimitReached\r\n            ? 'Max Limit Reached'\r\n            : !isRegistrationOpen\r\n            ? 'Registration Will Start Soon'\r\n            : 'Apply Now'}\r\n        </Button>\r\n      ) : examStatus === 'start' ? (\r\n        <Button\r\n          onClick={handleStartExam}\r\n          className=\"w-full bg-green-500 hover:bg-green-600 text-white font-semibold py-2 rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl\"\r\n          disabled={!isStartWindowActive || !hasApplied}\r\n        >\r\n          {hasApplied ? 'Start Exam Now' : 'You Have Not Applied'}\r\n        </Button>\r\n      ) : (\r\n        <Button\r\n        disabled\r\n          onClick={handleViewResult}\r\n          className=\"w-full bg-customOrange hover:bg-[#E88143] text-white font-semibold py-2 rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl\"\r\n        >\r\n          Result Will Announce Soon\r\n        </Button>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ExamStatusButton;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAAA;AACA;AAEA;AATA;;;;;;;;AAmBA,MAAM,mBAAmB,CAAC,EACxB,IAAI,EACJ,UAAU,EACV,iBAAiB,EACjB,YAAY,EACZ,YAAY,EACU;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,kBAAkB,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAqB,MAAM,IAAI,CAAC,eAAe;IACpF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsC;IACjF,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,MAAM,YAAY,IAAI,KAAK,KAAK,UAAU,EAAE,OAAO;YACnD,MAAM,wBAAwB,KAAK,uBAAuB,GACtD,IAAI,KAAK,KAAK,uBAAuB,EAAE,OAAO,KAC9C;YAEJ,IAAI,MAAM,YAAY;gBACpB,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,UAAU,EAAE;gBAC1E,cAAc;gBACd,uBAAuB;gBACvB,sBAAsB;gBACtB;YACF;YAEA,MAAM,MAAM,CAAA,GAAA,wKAAA,CAAA,cAAW,AAAD,EAAE,IAAI,QAAQ,gBAAgB,OAAO;YAC3D,MAAM,iBAAiB,YAAY,KAAK,QAAQ,GAAG,KAAK;YAExD,IAAI,yBAAyB,MAAM,uBAAuB;gBACxD,sBAAsB;YACxB,OAAO;gBACL,sBAAsB;YACxB;YAEA,IAAI,MAAM,WAAW;gBACnB,cAAc;gBACd,uBAAuB;YACzB,OAAO,IAAI,OAAO,aAAa,OAAO,gBAAgB;gBACpD,cAAc;gBACd,uBAAuB;YACzB,OACK;gBACH,cAAc;gBACd,uBAAuB;YACzB;QACF;QAEA;QACA,MAAM,WAAW,YAAY,iBAAiB;QAE9C,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC,KAAK,UAAU;QAAE,KAAK,QAAQ;QAAE,KAAK,EAAE;QAAE,KAAK,uBAAuB;KAAC;IAE1E,MAAM,kBAAkB;QACtB,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE;IACtC;IAEA,MAAM,mBAAmB;QACvB,OAAO,IAAI,CAAC,CAAC,eAAe,EAAE,KAAK,EAAE,EAAE;IACzC;IAEA,MAAM,mBAAmB;QACvB,IAAI,iBAAiB;YACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QACA;IACF;IAEA,IAAI,cAAc;QAChB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gBACL,WAAU;gBACV,QAAQ;0BACT;;;;;;;;;;;IAKP;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ,eAAe,4BACd,8OAAC,kIAAA,CAAA,SAAM;YACL,SAAS;YACT,WAAU;YACV,UAAU,qBAAqB,cAAc,CAAC;sBAE7C,aACG,YACA,oBACA,sBACA,CAAC,qBACD,iCACA;;;;;mBAEJ,eAAe,wBACjB,8OAAC,kIAAA,CAAA,SAAM;YACL,SAAS;YACT,WAAU;YACV,UAAU,CAAC,uBAAuB,CAAC;sBAElC,aAAa,mBAAmB;;;;;iCAGnC,8OAAC,kIAAA,CAAA,SAAM;YACP,QAAQ;YACN,SAAS;YACT,WAAU;sBACX;;;;;;;;;;;AAMT;uCAEe", "debugId": null}}, {"offset": {"line": 3487, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/referralApi.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\n\r\n// Get student discount information\r\nexport const getStudentDiscount = async () => {\r\n  try {\r\n    const response = await axiosInstance.get('/referral/discount/student');\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to get discount info: ${error.response?.data?.message || error.message}`,\r\n    };\r\n  }\r\n};\r\n\r\n// Calculate discounted price\r\nexport const calculateDiscountedPrice = (originalPrice: string, discountPercentage: number): number => {\r\n  const discountAmount = Math.round((Number(originalPrice) * discountPercentage) / 100);\r\n  return Number(originalPrice) - discountAmount;\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;;AAGO,MAAM,qBAAqB;IAChC,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;QACzC,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,6BAA6B,EAAE,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,EAAE;QACzF;IACF;AACF;AAGO,MAAM,2BAA2B,CAAC,eAAuB;IAC9D,MAAM,iBAAiB,KAAK,KAAK,CAAC,AAAC,OAAO,iBAAiB,qBAAsB;IACjF,OAAO,OAAO,iBAAiB;AACjC", "debugId": null}}, {"offset": {"line": 3514, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/PosterDialog.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport Image from 'next/image';\r\nimport React, { useRef, useEffect } from 'react';\r\n\r\ntype PosterDialogProps = {\r\n  open: boolean;\r\n  onClose: () => void;\r\n};\r\n\r\nexport default function PosterDialog({ open, onClose }: PosterDialogProps) {\r\n  const dialogRef = useRef<HTMLDialogElement>(null);\r\n\r\n  useEffect(() => {\r\n    const dialog = dialogRef.current;\r\n    if (!dialog) return;\r\n\r\n    if (open && !dialog.open) {\r\n      dialog.showModal();\r\n    } else if (!open && dialog.open) {\r\n      dialog.close();\r\n    }\r\n  }, [open]);\r\n\r\n  return (\r\n<dialog\r\n      ref={dialogRef}\r\n      className=\"w-[600px] h-[600px] p-0 border-none shadow-xl rounded-lg backdrop:bg-black/50 fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\"\r\n      onClose={onClose}\r\n    >\r\n      <div className=\"relative w-full h-full\">\r\n        {/* Close button */}\r\n        <button\r\n          className=\"absolute top-3 right-3 text-gray-600 hover:text-red-500 text-2xl z-10\"\r\n          onClick={onClose}\r\n        >\r\n          ×\r\n        </button>\r\n        <Image\r\n          src=\"/MathsMarvelWinner.png\"\r\n          alt=\"Uwhiz Winner\"\r\n          width={400}\r\n          height={250}\r\n          className=\"w-full h-full object-cover rounded-lg\"\r\n        />\r\n      </div>\r\n    </dialog>\r\n\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUe,SAAS,aAAa,EAAE,IAAI,EAAE,OAAO,EAAqB;IACvE,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAE5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QAEb,IAAI,QAAQ,CAAC,OAAO,IAAI,EAAE;YACxB,OAAO,SAAS;QAClB,OAAO,IAAI,CAAC,QAAQ,OAAO,IAAI,EAAE;YAC/B,OAAO,KAAK;QACd;IACF,GAAG;QAAC;KAAK;IAET,qBACF,8OAAC;QACK,KAAK;QACL,WAAU;QACV,SAAS;kBAET,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBACC,WAAU;oBACV,SAAS;8BACV;;;;;;8BAGD,8OAAC,6HAAA,CAAA,UAAK;oBACJ,KAAI;oBACJ,KAAI;oBACJ,OAAO;oBACP,QAAQ;oBACR,WAAU;;;;;;;;;;;;;;;;;AAMpB", "debugId": null}}, {"offset": {"line": 3582, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/uwhiz/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport Header from \"@/app-components/Header\";\r\nimport examLogo from \"../../../public/exam-logo.jpg\";\r\nimport { Card } from \"@/components/ui/card\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { getExams } from \"@/services/examApi\";\r\nimport { sendExamApplicantEmail } from \"@/services/examApplicantEmailApi\";\r\nimport { applyForExam } from \"@/services/examApplicationApi\";\r\nimport { useEffect, useState, useMemo } from \"react\";\r\nimport { Exam } from \"@/lib/types\";\r\nimport { FaRegClipboard, FaListOl, FaClock } from \"react-icons/fa6\";\r\nimport { toast } from \"sonner\";\r\nimport { uwhizPreventReattempApi } from \"@/services/uwhizPreventReattemptApi\";\r\nimport {\r\n  ChevronLeftIcon,\r\n  ChevronRightIcon,\r\n  ChevronsLeftIcon,\r\n  ChevronsRightIcon,\r\n  Coins,\r\n  Loader2,\r\n} from \"lucide-react\";\r\nimport Footer from \"@/app-components/Footer\";\r\nimport { GiPodiumWinner } from \"react-icons/gi\";\r\nimport { Progress } from \"@/components/ui/progress\";\r\nimport CountdownTimer from \"./countDownTimer\";\r\nimport ExamStatusButton from \"./examStatusButton\";\r\nimport Image from \"next/image\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { axiosInstance } from \"@/lib/axios\";\r\nimport { getStudentDiscount, calculateDiscountedPrice } from \"@/services/referralApi\";\r\nimport PosterDialog from \"@/components/ui/PosterDialog\";\r\n\r\nconst Page = () => {\r\n  const [exams, setExams] = useState<Exam[]>([]);\r\n  const [showSuccessDialog, setShowSuccessDialog] = useState(false);\r\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\r\n  const [selectedExam, setSelectedExam] = useState<Exam | null>(null);\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [totalPages, setTotalPages] = useState(1);\r\n  const [limit] = useState(9);\r\n  const [loading, setLoading] = useState(false);\r\n  const [coinError, setCoinError] = useState<string | null>(null);\r\n  const [discountInfo, setDiscountInfo] = useState<{\r\n    hasDiscount: boolean;\r\n    discountPercentage: number;\r\n    referralCode: string | null;\r\n  } | null>(null);\r\n  const [isPaying, setIsPaying] = useState(false);\r\n  const [remCoins, setRemCoins] = useState<number>(0);\r\n  const [isAdding, setIsAdding] = useState(false);\r\n  const [showPosterDialog, setShowPosterDialog] = useState(false);\r\n  const [superKidsError, setSuperKidsError] = useState<string | null>(null);\r\n\r\n\r\n  const isUpcomingExam = (exam: Exam) => {\r\n    const currentTime = new Date();\r\n    const startTime = new Date(exam.start_date);\r\n    const durationMs = exam.duration * 60 * 1000;\r\n    const endTime = new Date(startTime.getTime() + durationMs);\r\n    return endTime.getTime() > currentTime.getTime();\r\n  };\r\n\r\n  const isPastExam = (exam: Exam) => {\r\n    const currentTime = new Date();\r\n    const startTime = new Date(exam.start_date);\r\n    const durationMs = exam.duration * 60 * 1000;\r\n    const endTime = new Date(startTime.getTime() + durationMs);\r\n    return endTime.getTime() < currentTime.getTime();\r\n  };\r\n\r\n  const getStudentId = (): string => {\r\n    try {\r\n      const data = localStorage.getItem(\"student_data\");\r\n      return data ? JSON.parse(data).id : \"\";\r\n    } catch {\r\n      return \"\";\r\n    }\r\n  };\r\n\r\n  const handleOpenPosterDialog = () => {\r\n    setShowPosterDialog(true);\r\n  };\r\n\r\n  // Function to handle closing the PosterDialog\r\n  const handleClosePosterDialog = () => {\r\n    setShowPosterDialog(false);\r\n  };\r\n\r\n  const memoizedExams = useMemo(() => exams, [exams]);\r\n  const router = useRouter();\r\n  const classId = getStudentId();\r\n\r\n  const upcomingExams = memoizedExams.filter(isUpcomingExam);\r\n  const pastExams = memoizedExams.filter(isPastExam);\r\n\r\n  useEffect(() => {\r\n    const fetchDiscountInfo = async () => {\r\n      if (classId) {\r\n        try {\r\n          const response = await getStudentDiscount();\r\n          if (response.success) {\r\n            setDiscountInfo(response.data);\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Error fetching discount info:\", error);\r\n        }\r\n      }\r\n    };\r\n\r\n    fetchDiscountInfo();\r\n  }, [classId]);\r\n\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      setLoading(true);\r\n      try {\r\n        const response = await getExams(currentPage, limit, classId);\r\n        let examsData = response.exams as Exam[];\r\n\r\n        // Check attempt status for each exam\r\n        if (classId) {\r\n          examsData = await Promise.all(\r\n            examsData.map(async (exam) => {\r\n              try {\r\n                const attemptResponse = await uwhizPreventReattempApi(classId, exam.id);\r\n                return {\r\n                  ...exam,\r\n                  hasAttempted: attemptResponse.success === false ? false : attemptResponse,\r\n                };\r\n              } catch (error) {\r\n                console.error(`Error checking attempt for exam ${exam.id}:`, error);\r\n                return { ...exam, hasAttempted: false };\r\n              }\r\n            })\r\n          );\r\n        } else {\r\n          examsData = examsData.map((exam) => ({ ...exam, hasAttempted: false }));\r\n        }\r\n\r\n        setExams(examsData);\r\n        setTotalPages(response.totalPages || 1);\r\n      } catch (error: any) {\r\n        console.error(\"Error fetching exams:\", error);\r\n        toast.error(error.message || \"Failed to load exams\");\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchData();\r\n  }, [currentPage, limit, classId]);\r\n\r\n  const handleApplyClick = (exam: Exam) => {\r\n    setSelectedExam(exam);\r\n    setCoinError(null);\r\n    setShowConfirmDialog(true);\r\n  };\r\n\r\n\r\n  const fetchCoinData = async () => {\r\n    try {\r\n      const coinsResponse = await axiosInstance.get('/coins/get-total-coins/student');\r\n      return coinsResponse.data.coins;\r\n    } catch (error) {\r\n      toast.error('Failed to load coin data. Please try again.');\r\n      console.error('Error fetching data', error);\r\n    }\r\n  };\r\n\r\n  const handleApplyNow = async () => {\r\n    if (!selectedExam) return;\r\n    const classId = getStudentId();\r\n\r\n    if (!classId) {\r\n      toast.error(\"Please log in as a student to apply for an exam\");\r\n      return;\r\n    }\r\n\r\n    const totalCoins = await fetchCoinData();\r\n\r\n    try {\r\n      const applyRes = await applyForExam(selectedExam.id, classId);\r\n\r\n      if (applyRes.application) {\r\n        try {\r\n          const studentData = localStorage.getItem('student_data');\r\n          if (studentData) {\r\n            const parsedData = JSON.parse(studentData);\r\n            const studentEmail = parsedData.email;\r\n\r\n            if (studentEmail) {\r\n              await sendExamApplicantEmail(\r\n                selectedExam.id,\r\n                selectedExam.exam_name,\r\n                studentEmail\r\n              );\r\n            }\r\n          }\r\n        } catch (emailError) {\r\n          console.log(\"Email sending failed\", emailError);\r\n        }\r\n\r\n        setExams((prevExams) =>\r\n          prevExams.map((e) =>\r\n            e.id === selectedExam.id\r\n              ? {\r\n                ...e,\r\n                joinedClassesCount: e.joinedClassesCount + 1,\r\n                totalApplicants: (e.totalApplicants || 0) + 1,\r\n                hasApplied: true,\r\n              }\r\n              : e\r\n          )\r\n        );\r\n        setShowConfirmDialog(false);\r\n        setShowSuccessDialog(true);\r\n        toast.success(applyRes.message || \"Successfully applied for the exam\");\r\n        setCoinError(null);\r\n      }\r\n    } catch (error: any) {\r\n      const errorMessage = error?.response?.data?.error || error.message || \"Error applying for exam\";\r\n      toast.error(errorMessage);\r\n\r\n      if (errorMessage.includes(\"Uwhiz Super Kids Exam\")) {\r\n        setSuperKidsError(errorMessage);\r\n        setShowConfirmDialog(false);\r\n        return;\r\n      }\r\n\r\n      if (errorMessage.includes(\"Required Coin for Applying in Exam\")) {\r\n        setCoinError(errorMessage);\r\n\r\n        let coinsRequired = Number(selectedExam.coins_required) ?? 0;\r\n        if (discountInfo?.hasDiscount) {\r\n          coinsRequired = coinsRequired * (1 - discountInfo.discountPercentage / 100);\r\n        }\r\n        const remainingCoins = Math.floor(Math.floor(coinsRequired) - totalCoins);\r\n        setRemCoins(remainingCoins);\r\n      } else {\r\n        setShowConfirmDialog(false);\r\n      }\r\n    } finally {\r\n      setIsPaying(false);\r\n    }\r\n  };\r\n\r\n  const closeDialog = () => {\r\n    setShowSuccessDialog(false);\r\n    setShowConfirmDialog(false);\r\n    setSelectedExam(null);\r\n    setCoinError(null);\r\n  };\r\n\r\n  const handleViewDetails = (id: string) => {\r\n    window.location.href = `/uwhiz-info/${id}`;\r\n  };\r\n\r\n  const calculateProgress = (totalApplicants: number, totalIntake: number) => {\r\n    if (totalIntake === 0) return 0;\r\n    const percentage = (totalApplicants / totalIntake) * 100;\r\n    return Math.min(100, Math.max(0, percentage));\r\n  };\r\n\r\n  const initiatePayment = async () => {\r\n    setIsPaying(true);\r\n    try {\r\n      const res = await axiosInstance.post('/coins/create-order', {\r\n        amount: remCoins * 100,\r\n      });\r\n\r\n      const { order } = res.data;\r\n\r\n      const options = {\r\n        key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID,\r\n        amount: order.amount,\r\n        currency: 'INR',\r\n        name: 'Uest Coins',\r\n        description: 'Add Uest Coins',\r\n        order_id: order.id,\r\n        handler: async function (response: any) {\r\n          try {\r\n            setIsAdding(true);\r\n\r\n            await axiosInstance.post('/coins/verify', {\r\n              razorpay_order_id: response.razorpay_order_id,\r\n              razorpay_payment_id: response.razorpay_payment_id,\r\n              razorpay_signature: response.razorpay_signature,\r\n              amount: remCoins * 100,\r\n            });\r\n\r\n            toast.success('Coins added successfully!');\r\n            handleApplyNow();\r\n            setIsAdding(false);\r\n          } catch {\r\n            toast.error('Payment verification failed');\r\n          } finally {\r\n            setIsAdding(false);\r\n          }\r\n        },\r\n        theme: {\r\n          color: '#f97316',\r\n        },\r\n      };\r\n\r\n      const rzp = new (window as any).Razorpay(options);\r\n      rzp.open();\r\n    } catch {\r\n      toast.error('Payment initialization failed');\r\n    } finally {\r\n      setIsPaying(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const script = document.createElement('script');\r\n    script.src = 'https://checkout.razorpay.com/v1/checkout.js';\r\n    script.async = true;\r\n    document.body.appendChild(script);\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"\">\r\n      <Header />\r\n      <div className=\"flex justify-center bg-black pb-10\">\r\n        <Image\r\n          height={400}\r\n          width={400}\r\n          src={examLogo.src}\r\n          alt=\"Exam Logo\"\r\n          priority={true}\r\n          quality={100}\r\n        />\r\n      </div>\r\n\r\n      {showSuccessDialog && selectedExam && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n          <div className=\"bg-white p-6 rounded-lg shadow-xl max-w-md w-full\">\r\n            <h2 className=\"text-2xl font-bold text-green-600 mb-4\">\r\n              Application Successful!\r\n            </h2>\r\n            <p className=\"text-gray-700 mb-6\">\r\n              You have successfully applied for{\" \"}\r\n              <strong>{selectedExam.exam_name}</strong>.\r\n            </p>\r\n            <Button\r\n              onClick={closeDialog}\r\n              className=\"w-full bg-customOrange hover:bg-orange-600 text-white font-semibold py-2 rounded-lg\"\r\n            >\r\n              Close\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {showConfirmDialog && selectedExam && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n          <div className=\"bg-white p-8 rounded-xl shadow-2xl max-w-md w-full transform transition-all duration-300 scale-100\">\r\n            <h2 className=\"text-3xl font-semibold text-customOrange mb-4 border-b border-gray-200 pb-2\">\r\n              Are You Sure?\r\n            </h2>\r\n            <p className=\"text-gray-700 text-lg mb-6 leading-relaxed\">\r\n              Do you want to apply for{\" \"}\r\n              <strong className=\"text-customOrange\">{selectedExam.exam_name}</strong>?\r\n              {selectedExam.coins_required != null && (\r\n                <span>\r\n                  {\" \"}\r\n                  This will cost{\" \"}\r\n                  {discountInfo?.hasDiscount ? (\r\n                    <span>\r\n                      <span className=\"line-through text-gray-500\">{selectedExam.coins_required}</span>{\" \"}\r\n                      <strong className=\"text-green-600\">\r\n                        {calculateDiscountedPrice(selectedExam.coins_required, discountInfo.discountPercentage)}\r\n                      </strong>{\" \"}\r\n                      <span className=\"text-green-600 text-sm\">({discountInfo.discountPercentage}% discount applied)</span>\r\n                    </span>\r\n                  ) : (\r\n                    <strong className=\"text-customOrange\">{selectedExam.coins_required}</strong>\r\n                  )}{\" \"}\r\n                  coins.\r\n                </span>\r\n              )}\r\n            </p>\r\n            {coinError && (\r\n              <div className=\"flex-col justify-center items-start gap-2 bg-red-50 p-4 rounded-lg mb-6 border border-red-200\">\r\n                <div className=\"flex gap-5 items-center\">\r\n                  <svg\r\n                    className=\"w-5 h-5 text-red-600 mt-1\"\r\n                    fill=\"none\"\r\n                    stroke=\"currentColor\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      strokeWidth=\"2\"\r\n                      d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\r\n                    />\r\n                  </svg>\r\n                  <p className=\"text-red-600 text-sm font-medium\">{coinError}</p>\r\n                </div>\r\n                <Button\r\n                  onClick={() => initiatePayment()}\r\n                  className=\"mt-5 w-full bg-customOrange hover:bg-orange-600 text-white font-semibold py-3 rounded-lg\"\r\n                  disabled={isAdding}\r\n                >\r\n                  {isAdding ? (\r\n                    <span className=\"flex items-center justify-center gap-2\">\r\n                      <Loader2 className=\"animate-spin w-5 h-5\" />\r\n                      Processing...\r\n                    </span>\r\n                  ) : (\r\n                    \"Add Coins\"\r\n                  )}\r\n                </Button>\r\n              </div>\r\n            )}\r\n            {getStudentId() ? (\r\n              <div className=\"flex gap-4\">\r\n                <Button\r\n                  onClick={handleApplyNow}\r\n                  className=\"w-1/2 bg-customOrange hover:bg-orange-600 text-white font-semibold py-3 rounded-lg\"\r\n                  disabled={!!coinError || isPaying}\r\n                >\r\n                  {isPaying ? (\r\n                    <>\r\n                      <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\r\n                      Processing...\r\n                    </>\r\n                  ) : (\r\n                    \"Yes, Apply\"\r\n                  )}\r\n                </Button>\r\n                <Button\r\n                  onClick={closeDialog}\r\n                  className=\"w-1/2 bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-3 rounded-lg\"\r\n                >\r\n                  Cancel\r\n                </Button>\r\n              </div>\r\n            ) : (\r\n              <Button\r\n                onClick={() => router.push('/student/login?redirect=/uwhiz')}\r\n                className=\"w-full bg-customOrange hover:bg-orange-600 text-white font-semibold py-3 rounded-lg\"\r\n              >\r\n                Login to Apply\r\n              </Button>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {superKidsError && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n          <div className=\"bg-white dark:bg-gray-800 p-6 rounded-lg shadow-xl max-w-md w-full\">\r\n            <h2 className=\"text-2xl font-bold text-red-600 dark:text-red-400 mb-4\">\r\n              Access Denied\r\n            </h2>\r\n            <p className=\"text-gray-700 dark:text-gray-300 mb-6\">\r\n              {superKidsError}\r\n            </p>\r\n            <button\r\n              onClick={() => setSuperKidsError(null)}\r\n              className=\"w-full bg-customOrange hover:bg-orange-600 text-white font-semibold py-2 rounded-lg\"\r\n            >\r\n              Close\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n\r\n      {loading ? (\r\n        <p className=\"text-center text-white mt-6\">Loading exams...</p>\r\n      ) : (\r\n        <>\r\n          {/* Upcoming Exams section */}\r\n          {upcomingExams.length > 0 && (\r\n            <>\r\n              {upcomingExams.length > 0 && (\r\n                <div className=\"flex justify-center items-center mt-10\">\r\n                  <h1 className=\"text-3xl font-bold ml-4\">\r\n                    Upcoming <span className=\"text-customOrange\">Exams </span>\r\n                  </h1>\r\n                </div>\r\n              )}\r\n              <div className=\"flex flex-wrap justify-center items-start gap-6 p-4 sm:p-6 md:p-10 lg:p-20\">\r\n                {upcomingExams &&\r\n                  upcomingExams.map((exam) => {\r\n                    const totalIntake = exam.total_student_intake ?? 0;\r\n                    const firstRankPrice =\r\n                      exam.UwhizPriceRank.find((rank: any) => rank.rank === 1)?.price ?? 0;\r\n\r\n                    return (\r\n                      <Card\r\n                        key={exam.id}\r\n                        className=\"w-full sm:w-[45%] lg:w-[30%] dark:bg-[#101828] text-black rounded-xl shadow-lg hover:shadow-2xl hover:scale-105 transform transition-all duration-500 ease-out group border-l-3 border-l-orange-400\"\r\n                      >\r\n                        <div className=\"flex flex-col items-center px-3 py-2 space-y-2 text-center\">\r\n                          <h1 className=\"text-2xl font-bold dark:text-white transition-transform duration-300 group-hover:scale-105\">\r\n                            {exam.exam_name}\r\n                          </h1>\r\n                          {exam.id !== 4 && exam.id !== 6 && (\r\n                            <div className=\"flex items-center gap-2 text-xl font-bold\">\r\n                              <GiPodiumWinner className=\"text-xl text-customOrange\" />\r\n                              <span className=\"dark:text-white\">\r\n                                1st Prize: <span className=\"text-customOrange\">{firstRankPrice}</span>\r\n                              </span>\r\n                            </div>\r\n                          ) }\r\n                        </div>\r\n                        <CountdownTimer exam={exam} />\r\n                        <div className=\"text-sm dark:text-gray-100 mb-3 px-4 space-y-2 dark:group-hover:text-gray-200 transition-colors duration-300\">\r\n                          <div className=\"flex justify-between\">\r\n                            <div className=\"flex gap-2 items-center\">\r\n                              <FaListOl className=\"text-lg text-customOrange\" />\r\n                              <span>Total Questions: {exam.total_questions}</span>\r\n                            </div>\r\n                            <div className=\"flex gap-2 items-center\">\r\n                              <FaRegClipboard className=\"text-lg text-customOrange\" />\r\n                              <span>Marks: {exam.marks}</span>\r\n                            </div>\r\n                          </div>\r\n                          <div className=\"flex justify-between\">\r\n                            <div className=\"flex gap-2 items-center\">\r\n                              <FaClock className=\"text-lg text-customOrange\" />\r\n                              <span>Duration: {exam.duration}</span>\r\n                            </div>\r\n                            <div className=\"flex gap-2 items-center\">\r\n                              <Coins className=\"text-lg text-customOrange\" />\r\n                              <span>\r\n                                Coins: { \r\n                                (\r\n                            \r\n                                  discountInfo?.hasDiscount ? (\r\n                                    <span className=\"flex items-center gap-1\">\r\n                                      <span className=\"line-through text-gray-500 text-xs\">\r\n                                        {exam.coins_required}\r\n                                      </span>\r\n                                      <span className=\"text-green-600 font-bold\">\r\n                                        {calculateDiscountedPrice(exam.coins_required, discountInfo.discountPercentage)}\r\n                                      </span>\r\n                                      <span className=\"text-xs text-green-600\">\r\n                                        ({discountInfo.discountPercentage}% off)\r\n                                      </span>\r\n                                    </span>\r\n                                  ) : (\r\n                                   Number(exam.coins_required) === 0 ? \"Free\" :exam.coins_required  \r\n                                  )\r\n                                )}\r\n                              </span>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                        <div className=\"flex flex-col px-3\">\r\n                          <p className=\"dark:text-white mb-1 tracking-wider\">Student Joined</p>\r\n\r\n                          <Progress\r\n                            value={calculateProgress(((exam.totalApplicants ?? 0)), totalIntake)}\r\n                            className=\"[&>*]:bg-customOrange bg-slate-300\"\r\n                          />\r\n\r\n                          <p className=\"flex justify-end text-orange-500 text-sm dark:text-white\">\r\n                            <span>\r\n                              Limited Seats Available\r\n                            </span>\r\n                          </p>\r\n                        </div>\r\n                        <div className=\"flex justify-center px-3\">\r\n                          {\r\n                            exam.id === 1 && (\r\n                              <Button\r\n                                className=\"w-1/3 bg-customOrange hover:bg-[#E88143] text-white font-semibold rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl\"\r\n                                onClick={() => handleViewDetails(String(exam.id))}\r\n                              >\r\n                                View details\r\n                              </Button>\r\n                            )\r\n                          }\r\n                          <ExamStatusButton\r\n                            exam={exam}\r\n                            hasApplied={exam.hasApplied}\r\n                            isMaxLimitReached={exam.isMaxLimitReached}\r\n                            hasAttempted={exam.hasAttempted}\r\n                            onApplyClick={() => handleApplyClick(exam)}\r\n                          />\r\n                        </div>\r\n\r\n                        {exam.id === 7 && (\r\n                          <>\r\n                            <div className=\"bg-customOrange text-white font-semibold text-sm text-center p-3 flex items-center justify-center gap-2  border-orange-600 shadow-md \">\r\n                              <svg\r\n                                xmlns=\"http://www.w3.org/2000/svg\"\r\n                                className=\"h-5 w-5 text-white\"\r\n                                fill=\"none\"\r\n                                viewBox=\"0 0 24 24\"\r\n                                stroke=\"currentColor\"\r\n                              >\r\n                                <path\r\n                                  strokeLinecap=\"round\"\r\n                                  strokeLinejoin=\"round\"\r\n                                  strokeWidth={2}\r\n                                  d=\"M13 16h-1v-4h-1m1-4h.01M12 20.5a8.5 8.5 0 100-17 8.5 8.5 0 000 17z\"\r\n                                />\r\n                              </svg>\r\n                              <span>This exam is only for those who have participated in the Uwhiz Super Kids Exam.</span>\r\n                            </div>\r\n                          </>\r\n                        )}\r\n\r\n                        {exam.id === 1 ? (\r\n                          <div className=\"flex items-center justify-center gap-2 text-sm text-gray-600 dark:text-gray-300 pt-2 border-t border-gray-200 dark:border-gray-700\">\r\n                            <span>Sponsored by</span>\r\n                            <Image\r\n                              src=\"/nalanda.png\"\r\n                              alt=\"Nalanda Logo\"\r\n                              height={60}\r\n                              width={60}\r\n                              className=\"object-contain h-5 w-5\"\r\n                            />\r\n                            <span className=\"font-semibold\">Nalanda Vidhyalay</span>\r\n                          </div>\r\n                        ) : (null)}\r\n                      </Card>\r\n                    );\r\n                  })}\r\n              </div>\r\n            </>\r\n          )}\r\n\r\n          {/* Past Exams section */}\r\n          {pastExams.length > 0 &&\r\n            (\r\n              <>\r\n                <h1 className=\"text-center text-4xl font-bold dark:text-white mt-10\">\r\n                  Past <span className=\"text-customOrange\">Exams</span>\r\n                </h1>\r\n                {loading ? (\r\n                  <p className=\"text-center text-white mt-6\">Loading exams...</p>\r\n                ) : pastExams.length === 0 ? (\r\n                  <p className=\"text-center text-white mt-6\">No past exams found.</p>\r\n                ) : (\r\n                  <div className=\"flex flex-wrap justify-center gap-6 p-4 sm:p-6 md:p-10 lg:p-20\">\r\n                    {pastExams.map((exam) => {\r\n                      // const totalIntake = exam.total_student_intake ?? 0;\r\n                      const firstRankPrice =\r\n                        exam.UwhizPriceRank.find((rank: any) => rank.rank === 1)?.price ?? 0;\r\n\r\n                      return (\r\n                        <Card\r\n                          key={exam.id}\r\n                          className=\"w-full sm:w-[45%] lg:w-[30%] dark:bg-[#101828] text-black rounded-xl shadow-lg hover:shadow-2xl hover:scale-105 transform transition-all duration-500 ease-out group border-l-3 border-l-orange-400\"\r\n                        >\r\n                          <div className=\"flex flex-col items-center px-3 py-2 space-y-2 text-center\">\r\n                            <h1 className=\"text-2xl font-bold dark:text-white transition-transform duration-300 group-hover:scale-105\">\r\n                              {exam.exam_name}\r\n                            </h1>\r\n                            {(exam.id !== 4 && exam.id !== 6) && (\r\n                              <div className=\"flex items-center gap-2 text-xl font-bold\">\r\n                                <GiPodiumWinner className=\"text-xl text-customOrange\" />\r\n                                <span className=\"dark:text-white\">\r\n                                  1st Prize: <span className=\"text-customOrange\">{firstRankPrice}</span>\r\n                                </span>\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                          <CountdownTimer exam={exam} />\r\n                          <div className=\"text-sm dark:text-gray-100 mb-3 px-4 space-y-2 dark:group-hover:text-gray-200 transition-colors duration-300\">\r\n                            <div className=\"flex justify-between\">\r\n                              <div className=\"flex gap-2 items-center\">\r\n                                <FaListOl className=\"text-lg text-customOrange\" />\r\n                                <span>Total Questions: {exam.total_questions}</span>\r\n                              </div>\r\n                              <div className=\"flex gap-2 items-center\">\r\n                                <FaRegClipboard className=\"text-lg text-customOrange\" />\r\n                                <span>Marks: {exam.marks}</span>\r\n                              </div>\r\n                            </div>\r\n                            <div className=\"flex justify-between\">\r\n                              <div className=\"flex gap-2 items-center\">\r\n                                <FaClock className=\"text-lg text-customOrange\" />\r\n                                <span>Duration: {exam.duration}</span>\r\n                              </div>\r\n                              <div className=\"flex gap-2 items-center\">\r\n                                <Coins className=\"text-lg text-customOrange\" />\r\n                                <span>\r\n                                  Coins: {exam.coins_required != null ? (\r\n                                    discountInfo?.hasDiscount ? (\r\n                                      <span className=\"flex items-center gap-1\">\r\n                                        <span className=\"line-through text-gray-500 text-xs\">\r\n                                          {exam.coins_required}\r\n                                        </span>\r\n                                        <span className=\"text-green-600 font-bold\">\r\n                                          {calculateDiscountedPrice(exam.coins_required, discountInfo.discountPercentage)}\r\n                                        </span>\r\n                                        <span className=\"text-xs text-green-600\">\r\n                                          ({discountInfo.discountPercentage}% off)\r\n                                        </span>\r\n                                      </span>\r\n                                    ) : (\r\n                                      exam.coins_required\r\n                                    )\r\n                                  ) : \"Free\"}\r\n                                </span>\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                          {\r\n                            exam.id === 5 ? (\r\n                              <div className=\"flex flex-col px-3\">\r\n                                <p className=\"dark:text-white mb-1 tracking-wider\">Classes Joined</p>\r\n                                <Progress\r\n                                  value={100}\r\n                                  className=\"[&>*]:bg-customOrange bg-slate-300\"\r\n                                />\r\n                                <p className=\"flex justify-end text-orange-500 text-sm dark:text-white\">\r\n                                  <span>\r\n                                    Seats Full\r\n                                  </span>\r\n                                </p>\r\n                              </div>\r\n                            ) : (\r\n                              <div className=\"flex flex-col px-3\">\r\n                                <p className=\"dark:text-white mb-1 tracking-wider\">Student Joined</p>\r\n                                <Progress\r\n                                  value={100}\r\n                                  className=\"[&>*]:bg-customOrange bg-slate-300\"\r\n                                />\r\n                                <p className=\"flex justify-end text-orange-500 text-sm dark:text-white\">\r\n                                  <span>\r\n                                    {/* {exam.totalApplicants} / {totalIntake} */}\r\n                                    Seats Full\r\n                                  </span>\r\n                                </p>\r\n                              </div>\r\n                            )\r\n                          }\r\n                          <div className=\"flex justify-center px-3\">\r\n                            {exam.id === 1 && (\r\n\r\n                              <div className=\"flex gap-2 w-full justify-center\">\r\n                                <Button\r\n                                  className=\"w-1/3 bg-customOrange hover:bg-[#E88143] text-white font-semibold rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl\"\r\n                                  onClick={() => handleViewDetails(String(exam.id))}\r\n                                >\r\n                                  View details\r\n                                </Button>\r\n                                <Button\r\n                                  className=\"w-1/3 bg-customOrange hover:bg-[#E88143] text-white font-semibold rounded-lg transform transition-all duration-300 hover:-translate-y-1 hover:shadow-xl\"\r\n                                  onClick={() => router.push(`uwhiz-super-kids-result`)}\r\n                                >\r\n                                  View Result\r\n                                </Button>\r\n                              </div>\r\n                            )}\r\n                            {exam.id === 3 && (\r\n                              <>\r\n                                <Button\r\n                                  className=\"bg-customOrange mx-5\"\r\n                                  onClick={handleOpenPosterDialog}\r\n                                >\r\n                                  View Result\r\n                                </Button>\r\n                                <PosterDialog open={showPosterDialog} onClose={handleClosePosterDialog} />\r\n                              </>\r\n                            )}\r\n                            {exam.id === 5 ? (\r\n                              <Button\r\n                                className=\"bg-customOrange mx-5\"\r\n                                onClick={() => router.push(`/uwhiz-details/${5}`)}\r\n                              >\r\n                                View Result\r\n                              </Button>\r\n                            ) : exam.id !== 3 && exam.id !== 1 ? (\r\n                              <ExamStatusButton\r\n                                exam={exam}\r\n                                hasApplied={exam.hasApplied}\r\n                                isMaxLimitReached={exam.isMaxLimitReached}\r\n                                hasAttempted={exam.hasAttempted}\r\n                                onApplyClick={() => handleApplyClick(exam)}\r\n                              />\r\n                            ) : null}\r\n                          </div>\r\n                        </Card>\r\n                      );\r\n                    })}\r\n                  </div>\r\n                )}\r\n              </>\r\n            )}\r\n\r\n          {/* Pagination section */}\r\n          <div className=\"flex items-center justify-center px-4 py-6 dark:text-white\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"icon\"\r\n                onClick={() => setCurrentPage(1)}\r\n                disabled={currentPage === 1}\r\n                className=\"bg-white dark:text-white hover:bg-gray-200 cursor-pointer\"\r\n              >\r\n                <ChevronsLeftIcon />\r\n              </Button>\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"icon\"\r\n                onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}\r\n                disabled={currentPage === 1}\r\n                className=\"bg-white dark:text-white hover:bg-gray-200 cursor-pointer\"\r\n              >\r\n                <ChevronLeftIcon />\r\n              </Button>\r\n              <span className=\"text-sm\">\r\n                Page {currentPage} of {totalPages}\r\n              </span>\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"icon\"\r\n                onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}\r\n                disabled={currentPage === totalPages}\r\n                className=\"hover:bg-gray-200 cursor-pointer\"\r\n              >\r\n                <ChevronRightIcon />\r\n              </Button>\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"icon\"\r\n                onClick={() => setCurrentPage(totalPages)}\r\n                disabled={currentPage === totalPages}\r\n                className=\"bg-white dark:text-white hover:bg-gray-200 cursor-pointer\"\r\n              >\r\n                <ChevronsRightIcon />\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </>\r\n      )}\r\n      <Footer />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Page;"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA9BA;;;;;;;;;;;;;;;;;;;;;;;;AAgCA,MAAM,OAAO;IACX,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,MAAM,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAIrC;IACV,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAGpE,MAAM,iBAAiB,CAAC;QACtB,MAAM,cAAc,IAAI;QACxB,MAAM,YAAY,IAAI,KAAK,KAAK,UAAU;QAC1C,MAAM,aAAa,KAAK,QAAQ,GAAG,KAAK;QACxC,MAAM,UAAU,IAAI,KAAK,UAAU,OAAO,KAAK;QAC/C,OAAO,QAAQ,OAAO,KAAK,YAAY,OAAO;IAChD;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,cAAc,IAAI;QACxB,MAAM,YAAY,IAAI,KAAK,KAAK,UAAU;QAC1C,MAAM,aAAa,KAAK,QAAQ,GAAG,KAAK;QACxC,MAAM,UAAU,IAAI,KAAK,UAAU,OAAO,KAAK;QAC/C,OAAO,QAAQ,OAAO,KAAK,YAAY,OAAO;IAChD;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,OAAO,aAAa,OAAO,CAAC;YAClC,OAAO,OAAO,KAAK,KAAK,CAAC,MAAM,EAAE,GAAG;QACtC,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,MAAM,yBAAyB;QAC7B,oBAAoB;IACtB;IAEA,8CAA8C;IAC9C,MAAM,0BAA0B;QAC9B,oBAAoB;IACtB;IAEA,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,OAAO;QAAC;KAAM;IAClD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,UAAU;IAEhB,MAAM,gBAAgB,cAAc,MAAM,CAAC;IAC3C,MAAM,YAAY,cAAc,MAAM,CAAC;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB;YACxB,IAAI,SAAS;gBACX,IAAI;oBACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,qBAAkB,AAAD;oBACxC,IAAI,SAAS,OAAO,EAAE;wBACpB,gBAAgB,SAAS,IAAI;oBAC/B;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,iCAAiC;gBACjD;YACF;QACF;QAEA;IACF,GAAG;QAAC;KAAQ;IAEZ,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,WAAW;YACX,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,0HAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,OAAO;gBACpD,IAAI,YAAY,SAAS,KAAK;gBAE9B,qCAAqC;gBACrC,IAAI,SAAS;oBACX,YAAY,MAAM,QAAQ,GAAG,CAC3B,UAAU,GAAG,CAAC,OAAO;wBACnB,IAAI;4BACF,MAAM,kBAAkB,MAAM,CAAA,GAAA,2IAAA,CAAA,0BAAuB,AAAD,EAAE,SAAS,KAAK,EAAE;4BACtE,OAAO;gCACL,GAAG,IAAI;gCACP,cAAc,gBAAgB,OAAO,KAAK,QAAQ,QAAQ;4BAC5D;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE;4BAC7D,OAAO;gCAAE,GAAG,IAAI;gCAAE,cAAc;4BAAM;wBACxC;oBACF;gBAEJ,OAAO;oBACL,YAAY,UAAU,GAAG,CAAC,CAAC,OAAS,CAAC;4BAAE,GAAG,IAAI;4BAAE,cAAc;wBAAM,CAAC;gBACvE;gBAEA,SAAS;gBACT,cAAc,SAAS,UAAU,IAAI;YACvC,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC/B,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;QAAa;QAAO;KAAQ;IAEhC,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,aAAa;QACb,qBAAqB;IACvB;IAGA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,gBAAgB,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;YAC9C,OAAO,cAAc,IAAI,CAAC,KAAK;QACjC,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,uBAAuB;QACvC;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,cAAc;QACnB,MAAM,UAAU;QAEhB,IAAI,CAAC,SAAS;YACZ,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,aAAa,MAAM;QAEzB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,qIAAA,CAAA,eAAY,AAAD,EAAE,aAAa,EAAE,EAAE;YAErD,IAAI,SAAS,WAAW,EAAE;gBACxB,IAAI;oBACF,MAAM,cAAc,aAAa,OAAO,CAAC;oBACzC,IAAI,aAAa;wBACf,MAAM,aAAa,KAAK,KAAK,CAAC;wBAC9B,MAAM,eAAe,WAAW,KAAK;wBAErC,IAAI,cAAc;4BAChB,MAAM,CAAA,GAAA,wIAAA,CAAA,yBAAsB,AAAD,EACzB,aAAa,EAAE,EACf,aAAa,SAAS,EACtB;wBAEJ;oBACF;gBACF,EAAE,OAAO,YAAY;oBACnB,QAAQ,GAAG,CAAC,wBAAwB;gBACtC;gBAEA,SAAS,CAAC,YACR,UAAU,GAAG,CAAC,CAAC,IACb,EAAE,EAAE,KAAK,aAAa,EAAE,GACpB;4BACA,GAAG,CAAC;4BACJ,oBAAoB,EAAE,kBAAkB,GAAG;4BAC3C,iBAAiB,CAAC,EAAE,eAAe,IAAI,CAAC,IAAI;4BAC5C,YAAY;wBACd,IACE;gBAGR,qBAAqB;gBACrB,qBAAqB;gBACrB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS,OAAO,IAAI;gBAClC,aAAa;YACf;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,eAAe,OAAO,UAAU,MAAM,SAAS,MAAM,OAAO,IAAI;YACtE,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YAEZ,IAAI,aAAa,QAAQ,CAAC,0BAA0B;gBAClD,kBAAkB;gBAClB,qBAAqB;gBACrB;YACF;YAEA,IAAI,aAAa,QAAQ,CAAC,uCAAuC;gBAC/D,aAAa;gBAEb,IAAI,gBAAgB,OAAO,aAAa,cAAc,KAAK;gBAC3D,IAAI,cAAc,aAAa;oBAC7B,gBAAgB,gBAAgB,CAAC,IAAI,aAAa,kBAAkB,GAAG,GAAG;gBAC5E;gBACA,MAAM,iBAAiB,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,iBAAiB;gBAC9D,YAAY;YACd,OAAO;gBACL,qBAAqB;YACvB;QACF,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,cAAc;QAClB,qBAAqB;QACrB,qBAAqB;QACrB,gBAAgB;QAChB,aAAa;IACf;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,YAAY,EAAE,IAAI;IAC5C;IAEA,MAAM,oBAAoB,CAAC,iBAAyB;QAClD,IAAI,gBAAgB,GAAG,OAAO;QAC9B,MAAM,aAAa,AAAC,kBAAkB,cAAe;QACrD,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG;IACnC;IAEA,MAAM,kBAAkB;QACtB,YAAY;QACZ,IAAI;YACF,MAAM,MAAM,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB;gBAC1D,QAAQ,WAAW;YACrB;YAEA,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,IAAI;YAE1B,MAAM,UAAU;gBACd,GAAG;gBACH,QAAQ,MAAM,MAAM;gBACpB,UAAU;gBACV,MAAM;gBACN,aAAa;gBACb,UAAU,MAAM,EAAE;gBAClB,SAAS,eAAgB,QAAa;oBACpC,IAAI;wBACF,YAAY;wBAEZ,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,iBAAiB;4BACxC,mBAAmB,SAAS,iBAAiB;4BAC7C,qBAAqB,SAAS,mBAAmB;4BACjD,oBAAoB,SAAS,kBAAkB;4BAC/C,QAAQ,WAAW;wBACrB;wBAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wBACd;wBACA,YAAY;oBACd,EAAE,OAAM;wBACN,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACd,SAAU;wBACR,YAAY;oBACd;gBACF;gBACA,OAAO;oBACL,OAAO;gBACT;YACF;YAEA,MAAM,MAAM,IAAI,AAAC,OAAe,QAAQ,CAAC;YACzC,IAAI,IAAI;QACV,EAAE,OAAM;YACN,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,YAAY;QACd;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,OAAO,GAAG,GAAG;QACb,OAAO,KAAK,GAAG;QACf,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,mIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oBACJ,QAAQ;oBACR,OAAO;oBACP,KAAK,4QAAA,CAAA,UAAQ,CAAC,GAAG;oBACjB,KAAI;oBACJ,UAAU;oBACV,SAAS;;;;;;;;;;;YAIZ,qBAAqB,8BACpB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,8OAAC;4BAAE,WAAU;;gCAAqB;gCACE;8CAClC,8OAAC;8CAAQ,aAAa,SAAS;;;;;;gCAAU;;;;;;;sCAE3C,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;YAON,qBAAqB,8BACpB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA8E;;;;;;sCAG5F,8OAAC;4BAAE,WAAU;;gCAA6C;gCAC/B;8CACzB,8OAAC;oCAAO,WAAU;8CAAqB,aAAa,SAAS;;;;;;gCAAU;gCACtE,aAAa,cAAc,IAAI,sBAC9B,8OAAC;;wCACE;wCAAI;wCACU;wCACd,cAAc,4BACb,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAA8B,aAAa,cAAc;;;;;;gDAAS;8DAClF,8OAAC;oDAAO,WAAU;8DACf,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,aAAa,cAAc,EAAE,aAAa,kBAAkB;;;;;;gDAC9E;8DACV,8OAAC;oDAAK,WAAU;;wDAAyB;wDAAE,aAAa,kBAAkB;wDAAC;;;;;;;;;;;;iEAG7E,8OAAC;4CAAO,WAAU;sDAAqB,aAAa,cAAc;;;;;;wCACjE;wCAAI;;;;;;;;;;;;;wBAKZ,2BACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,WAAU;4CACV,MAAK;4CACL,QAAO;4CACP,SAAQ;4CACR,OAAM;sDAEN,cAAA,8OAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAY;gDACZ,GAAE;;;;;;;;;;;sDAGN,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;8CAEnD,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,IAAM;oCACf,WAAU;oCACV,UAAU;8CAET,yBACC,8OAAC;wCAAK,WAAU;;0DACd,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAyB;;;;;;+CAI9C;;;;;;;;;;;;wBAKP,+BACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,WAAU;oCACV,UAAU,CAAC,CAAC,aAAa;8CAExB,yBACC;;0DACE,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA8B;;uDAInD;;;;;;8CAGJ,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;iDAKH,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS,IAAM,OAAO,IAAI,CAAC;4BAC3B,WAAU;sCACX;;;;;;;;;;;;;;;;;YAQR,gCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,8OAAC;4BAAE,WAAU;sCACV;;;;;;sCAEH,8OAAC;4BACC,SAAS,IAAM,kBAAkB;4BACjC,WAAU;sCACX;;;;;;;;;;;;;;;;;YAQN,wBACC,8OAAC;gBAAE,WAAU;0BAA8B;;;;;qCAE3C;;oBAEG,cAAc,MAAM,GAAG,mBACtB;;4BACG,cAAc,MAAM,GAAG,mBACtB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;;wCAA0B;sDAC7B,8OAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;;;;;;0CAInD,8OAAC;gCAAI,WAAU;0CACZ,iBACC,cAAc,GAAG,CAAC,CAAC;oCACjB,MAAM,cAAc,KAAK,oBAAoB,IAAI;oCACjD,MAAM,iBACJ,KAAK,cAAc,CAAC,IAAI,CAAC,CAAC,OAAc,KAAK,IAAI,KAAK,IAAI,SAAS;oCAErE,qBACE,8OAAC,gIAAA,CAAA,OAAI;wCAEH,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,KAAK,SAAS;;;;;;oDAEhB,KAAK,EAAE,KAAK,KAAK,KAAK,EAAE,KAAK,mBAC5B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,8IAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;0EAC1B,8OAAC;gEAAK,WAAU;;oEAAkB;kFACrB,8OAAC;wEAAK,WAAU;kFAAqB;;;;;;;;;;;;;;;;;;;;;;;;0DAKxD,8OAAC,sIAAA,CAAA,UAAc;gDAAC,MAAM;;;;;;0DACtB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,+IAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,8OAAC;;4EAAK;4EAAkB,KAAK,eAAe;;;;;;;;;;;;;0EAE9C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,+IAAA,CAAA,iBAAc;wEAAC,WAAU;;;;;;kFAC1B,8OAAC;;4EAAK;4EAAQ,KAAK,KAAK;;;;;;;;;;;;;;;;;;;kEAG5B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,+IAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;kFACnB,8OAAC;;4EAAK;4EAAW,KAAK,QAAQ;;;;;;;;;;;;;0EAEhC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,8OAAC;;4EAAK;4EAIF,cAAc,4BACZ,8OAAC;gFAAK,WAAU;;kGACd,8OAAC;wFAAK,WAAU;kGACb,KAAK,cAAc;;;;;;kGAEtB,8OAAC;wFAAK,WAAU;kGACb,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,KAAK,cAAc,EAAE,aAAa,kBAAkB;;;;;;kGAEhF,8OAAC;wFAAK,WAAU;;4FAAyB;4FACrC,aAAa,kBAAkB;4FAAC;;;;;;;;;;;;uFAIvC,OAAO,KAAK,cAAc,MAAM,IAAI,SAAQ,KAAK,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;0DAO1E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAsC;;;;;;kEAEnD,8OAAC,oIAAA,CAAA,WAAQ;wDACP,OAAO,kBAAoB,KAAK,eAAe,IAAI,GAAK;wDACxD,WAAU;;;;;;kEAGZ,8OAAC;wDAAE,WAAU;kEACX,cAAA,8OAAC;sEAAK;;;;;;;;;;;;;;;;;0DAKV,8OAAC;gDAAI,WAAU;;oDAEX,KAAK,EAAE,KAAK,mBACV,8OAAC,kIAAA,CAAA,SAAM;wDACL,WAAU;wDACV,SAAS,IAAM,kBAAkB,OAAO,KAAK,EAAE;kEAChD;;;;;;kEAKL,8OAAC,wIAAA,CAAA,UAAgB;wDACf,MAAM;wDACN,YAAY,KAAK,UAAU;wDAC3B,mBAAmB,KAAK,iBAAiB;wDACzC,cAAc,KAAK,YAAY;wDAC/B,cAAc,IAAM,iBAAiB;;;;;;;;;;;;4CAIxC,KAAK,EAAE,KAAK,mBACX;0DACE,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,OAAM;4DACN,WAAU;4DACV,MAAK;4DACL,SAAQ;4DACR,QAAO;sEAEP,cAAA,8OAAC;gEACC,eAAc;gEACd,gBAAe;gEACf,aAAa;gEACb,GAAE;;;;;;;;;;;sEAGN,8OAAC;sEAAK;;;;;;;;;;;;;4CAKX,KAAK,EAAE,KAAK,kBACX,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAK;;;;;;kEACN,8OAAC,6HAAA,CAAA,UAAK;wDACJ,KAAI;wDACJ,KAAI;wDACJ,QAAQ;wDACR,OAAO;wDACP,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;uDAE/B;;uCA/HA,KAAK,EAAE;;;;;gCAkIlB;;;;;;;;oBAMP,UAAU,MAAM,GAAG,mBAEhB;;0CACE,8OAAC;gCAAG,WAAU;;oCAAuD;kDAC9D,8OAAC;wCAAK,WAAU;kDAAoB;;;;;;;;;;;;4BAE1C,wBACC,8OAAC;gCAAE,WAAU;0CAA8B;;;;;uCACzC,UAAU,MAAM,KAAK,kBACvB,8OAAC;gCAAE,WAAU;0CAA8B;;;;;qDAE3C,8OAAC;gCAAI,WAAU;0CACZ,UAAU,GAAG,CAAC,CAAC;oCACd,sDAAsD;oCACtD,MAAM,iBACJ,KAAK,cAAc,CAAC,IAAI,CAAC,CAAC,OAAc,KAAK,IAAI,KAAK,IAAI,SAAS;oCAErE,qBACE,8OAAC,gIAAA,CAAA,OAAI;wCAEH,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,KAAK,SAAS;;;;;;oDAEf,KAAK,EAAE,KAAK,KAAK,KAAK,EAAE,KAAK,mBAC7B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,8IAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;0EAC1B,8OAAC;gEAAK,WAAU;;oEAAkB;kFACrB,8OAAC;wEAAK,WAAU;kFAAqB;;;;;;;;;;;;;;;;;;;;;;;;0DAKxD,8OAAC,sIAAA,CAAA,UAAc;gDAAC,MAAM;;;;;;0DACtB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,+IAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,8OAAC;;4EAAK;4EAAkB,KAAK,eAAe;;;;;;;;;;;;;0EAE9C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,+IAAA,CAAA,iBAAc;wEAAC,WAAU;;;;;;kFAC1B,8OAAC;;4EAAK;4EAAQ,KAAK,KAAK;;;;;;;;;;;;;;;;;;;kEAG5B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,+IAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;kFACnB,8OAAC;;4EAAK;4EAAW,KAAK,QAAQ;;;;;;;;;;;;;0EAEhC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,8OAAC;;4EAAK;4EACI,KAAK,cAAc,IAAI,OAC7B,cAAc,4BACZ,8OAAC;gFAAK,WAAU;;kGACd,8OAAC;wFAAK,WAAU;kGACb,KAAK,cAAc;;;;;;kGAEtB,8OAAC;wFAAK,WAAU;kGACb,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE,KAAK,cAAc,EAAE,aAAa,kBAAkB;;;;;;kGAEhF,8OAAC;wFAAK,WAAU;;4FAAyB;4FACrC,aAAa,kBAAkB;4FAAC;;;;;;;;;;;;uFAItC,KAAK,cAAc,GAEnB;;;;;;;;;;;;;;;;;;;;;;;;;4CAMV,KAAK,EAAE,KAAK,kBACV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAsC;;;;;;kEACnD,8OAAC,oIAAA,CAAA,WAAQ;wDACP,OAAO;wDACP,WAAU;;;;;;kEAEZ,8OAAC;wDAAE,WAAU;kEACX,cAAA,8OAAC;sEAAK;;;;;;;;;;;;;;;;qEAMV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAsC;;;;;;kEACnD,8OAAC,oIAAA,CAAA,WAAQ;wDACP,OAAO;wDACP,WAAU;;;;;;kEAEZ,8OAAC;wDAAE,WAAU;kEACX,cAAA,8OAAC;sEAC+C;;;;;;;;;;;;;;;;;0DAOxD,8OAAC;gDAAI,WAAU;;oDACZ,KAAK,EAAE,KAAK,mBAEX,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEACL,WAAU;gEACV,SAAS,IAAM,kBAAkB,OAAO,KAAK,EAAE;0EAChD;;;;;;0EAGD,8OAAC,kIAAA,CAAA,SAAM;gEACL,WAAU;gEACV,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,uBAAuB,CAAC;0EACrD;;;;;;;;;;;;oDAKJ,KAAK,EAAE,KAAK,mBACX;;0EACE,8OAAC,kIAAA,CAAA,SAAM;gEACL,WAAU;gEACV,SAAS;0EACV;;;;;;0EAGD,8OAAC,wIAAA,CAAA,UAAY;gEAAC,MAAM;gEAAkB,SAAS;;;;;;;;oDAGlD,KAAK,EAAE,KAAK,kBACX,8OAAC,kIAAA,CAAA,SAAM;wDACL,WAAU;wDACV,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,eAAe,EAAE,GAAG;kEACjD;;;;;+DAGC,KAAK,EAAE,KAAK,KAAK,KAAK,EAAE,KAAK,kBAC/B,8OAAC,wIAAA,CAAA,UAAgB;wDACf,MAAM;wDACN,YAAY,KAAK,UAAU;wDAC3B,mBAAmB,KAAK,iBAAiB;wDACzC,cAAc,KAAK,YAAY;wDAC/B,cAAc,IAAM,iBAAiB;;;;;+DAErC;;;;;;;;uCAnID,KAAK,EAAE;;;;;gCAuIlB;;;;;;;;kCAOV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,eAAe;oCAC9B,UAAU,gBAAgB;oCAC1B,WAAU;8CAEV,cAAA,8OAAC,0NAAA,CAAA,mBAAgB;;;;;;;;;;8CAEnB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,eAAe,CAAC,OAAS,KAAK,GAAG,CAAC,OAAO,GAAG;oCAC3D,UAAU,gBAAgB;oCAC1B,WAAU;8CAEV,cAAA,8OAAC,wNAAA,CAAA,kBAAe;;;;;;;;;;8CAElB,8OAAC;oCAAK,WAAU;;wCAAU;wCAClB;wCAAY;wCAAK;;;;;;;8CAEzB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,eAAe,CAAC,OAAS,KAAK,GAAG,CAAC,OAAO,GAAG;oCAC3D,UAAU,gBAAgB;oCAC1B,WAAU;8CAEV,cAAA,8OAAC,0NAAA,CAAA,mBAAgB;;;;;;;;;;8CAEnB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,eAAe;oCAC9B,UAAU,gBAAgB;oCAC1B,WAAU;8CAEV,cAAA,8OAAC,4NAAA,CAAA,oBAAiB;;;;;;;;;;;;;;;;;;;;;;;0BAM5B,8OAAC,mIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;uCAEe", "debugId": null}}]}