import { axiosInstance } from '@/lib/axios';
import {
  StoreOrderFilters,
  StoreOrderPaginationResponse,
  ShippingAddress,
  AddressValidationResponse
} from '@/lib/types';

export const getAllStoreOrders = async (
  page: number = 1,
  limit: number = 10,
  filters?: StoreOrderFilters
): Promise<StoreOrderPaginationResponse | { success: false; error: string }> => {
  try {
    const params: any = { page, limit };
    if (filters?.status) params.status = filters.status;
    if (filters?.search) params.search = filters.search;
    if (filters?.modelType) params.modelType = filters.modelType;
    if (filters?.hasAddress !== undefined) params.hasAddress = filters.hasAddress;
    if (filters?.missingAddress !== undefined) params.missingAddress = filters.missingAddress;
    if (filters?.city) params.city = filters.city;
    if (filters?.state) params.state = filters.state;
    if (filters?.country) params.country = filters.country;

    const response = await axiosInstance.get('/admin/store/orders', { params });
    console.log("all product",response.data.data);
    return response.data.data;
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to fetch store orders'
    };
  }
};

export const getStoreOrderStats = async () => {
  try {
    const response = await axiosInstance.get('/admin/store/orders/stats');
    return response.data.data;
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to fetch store order statistics'
    };
  }
};

export const getStoreOrderById = async (orderId: string) => {
  try {
    const response = await axiosInstance.get(`/admin/store/orders/${orderId}`);
    return response.data.data;
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to fetch store order'
    };
  }
};

export const updateStoreOrderStatus = async (orderId: string, status: string) => {
  try {
    const response = await axiosInstance.put(`/admin/store/orders/${orderId}`, { status });
    return response.data.data;
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to update store order status'
    };
  }
};

export const updateStoreOrderAddress = async (orderId: string, shippingAddress: ShippingAddress) => {
  try {
    const response = await axiosInstance.put(`/admin/store/orders/${orderId}/address`, { shippingAddress });
    return response.data.data;
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to update store order address'
    };
  }
};

export const getStoreOrderAddress = async (orderId: string) => {
  try {
    const response = await axiosInstance.get(`/admin/store/orders/${orderId}/address`);
    return response.data.data;
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to fetch store order address'
    };
  }
};

export const getOrdersWithoutAddress = async (
  page: number = 1,
  limit: number = 10
) => {
  try {
    const params = { page, limit, missingAddress: true };
    const response = await axiosInstance.get('/admin/store/orders', { params });
    return response.data.data;
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to fetch orders without address'
    };
  }
};

export const bulkUpdateOrderAddresses = async (updates: Array<{
  orderId: string;
  shippingAddress: ShippingAddress;
}>) => {
  try {
    const response = await axiosInstance.put('/admin/store/orders/bulk-address-update', { updates });
    return response.data.data;
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to bulk update order addresses'
    };
  }
};

export const validateOrderAddress = async (orderId: string): Promise<AddressValidationResponse | { success: false; error: string }> => {
  try {
    const response = await axiosInstance.post(`/admin/store/orders/${orderId}/validate-address`);
    return response.data.data;
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to validate order address'
    };
  }
};

export const exportOrderAddresses = async (filters?: StoreOrderFilters) => {
  try {
    const params: any = {};
    if (filters?.status) params.status = filters.status;
    if (filters?.search) params.search = filters.search;
    if (filters?.modelType) params.modelType = filters.modelType;
    if (filters?.hasAddress !== undefined) params.hasAddress = filters.hasAddress;
    if (filters?.missingAddress !== undefined) params.missingAddress = filters.missingAddress;
    if (filters?.city) params.city = filters.city;
    if (filters?.state) params.state = filters.state;
    if (filters?.country) params.country = filters.country;

    const response = await axiosInstance.get('/admin/store/orders/export-addresses', {
      params,
      responseType: 'blob'
    });
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to export order addresses'
    };
  }
};

export const getAddressStatistics = async () => {
  try {
    const response = await axiosInstance.get('/admin/store/orders/address-stats');
    return response.data.data;
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to fetch address statistics'
    };
  }
};
