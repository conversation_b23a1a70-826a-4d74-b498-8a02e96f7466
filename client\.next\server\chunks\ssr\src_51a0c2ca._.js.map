{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx';\r\nimport { twMerge } from 'tailwind-merge';\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport const truncateThought = (text: string, wordLimit: number = 5): string => {\r\n  const words = text.trim().split(/\\s+/);\r\n  if (words.length <= wordLimit) return text;\r\n  return words.slice(0, wordLimit).join(' ') + '...';\r\n};\r\n\r\nexport const setStudentAuthToken = (token: string) => {\r\n  localStorage.setItem('studentToken', token);\r\n};\r\n\r\nexport const getStudentAuthToken = (): string | null => {\r\n  if (typeof window !== 'undefined') {\r\n    return localStorage.getItem('studentToken');\r\n  }\r\n  return null;\r\n};\r\n\r\nexport const clearStudentAuthToken = () => {\r\n  localStorage.removeItem('studentToken');\r\n};\r\n\r\nexport const isStudentAuthenticated = (): boolean => {\r\n  return !!getStudentAuthToken();\r\n};\r\n\r\nexport const isAuthenticated = (): { isAuth: boolean; userType: 'STUDENT' | 'CLASS' | null } => {\r\n  const studentToken = getStudentAuthToken();\r\n  if (studentToken) {\r\n    return { isAuth: true, userType: 'STUDENT' };\r\n  }\r\n\r\n  if (typeof window !== 'undefined') {\r\n    const userData = localStorage.getItem('user');\r\n    if (userData) {\r\n      try {\r\n        const user = JSON.parse(userData);\r\n        if (user && user.id) {\r\n          return { isAuth: true, userType: 'CLASS' };\r\n        }\r\n      } catch (error) {\r\n        console.error('Error parsing user data:', error);\r\n      }\r\n    }\r\n  }\r\n\r\n  return { isAuth: false, userType: null };\r\n};"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,kBAAkB,CAAC,MAAc,YAAoB,CAAC;IACjE,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC;IAChC,IAAI,MAAM,MAAM,IAAI,WAAW,OAAO;IACtC,OAAO,MAAM,KAAK,CAAC,GAAG,WAAW,IAAI,CAAC,OAAO;AAC/C;AAEO,MAAM,sBAAsB,CAAC;IAClC,aAAa,OAAO,CAAC,gBAAgB;AACvC;AAEO,MAAM,sBAAsB;IACjC,uCAAmC;;IAEnC;IACA,OAAO;AACT;AAEO,MAAM,wBAAwB;IACnC,aAAa,UAAU,CAAC;AAC1B;AAEO,MAAM,yBAAyB;IACpC,OAAO,CAAC,CAAC;AACX;AAEO,MAAM,kBAAkB;IAC7B,MAAM,eAAe;IACrB,IAAI,cAAc;QAChB,OAAO;YAAE,QAAQ;YAAM,UAAU;QAAU;IAC7C;IAEA,uCAAmC;;IAYnC;IAEA,OAAO;QAAE,QAAQ;QAAO,UAAU;IAAK;AACzC", "debugId": null}}, {"offset": {"line": 65, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\r\n        destructive:\r\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline:\r\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\r\n        secondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\r\n        ghost: 'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\r\n        link: 'text-primary underline-offset-4 hover:underline',\r\n      },\r\n      size: {\r\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\r\n        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\r\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\r\n        icon: 'size-9',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default',\r\n    },\r\n  }\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : 'button';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn('leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return <div data-slot=\"card-content\" className={cn('px-6', className)} {...props} />;\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kEAAkE;QAC/E,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBAAO,8OAAC;QAAI,aAAU;QAAe,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAa,GAAG,KAAK;;;;;;AAClF;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst badgeVariants = cva(\r\n  'inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden',\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: 'border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90',\r\n        secondary:\r\n          'border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90',\r\n        destructive:\r\n          'border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline: 'text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n    },\r\n  }\r\n);\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'span'> & VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : 'span';\r\n\r\n  return (\r\n    <Comp data-slot=\"badge\" className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  );\r\n}\r\n\r\nexport { Badge, badgeVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OACuF;IAC1F,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QAAK,aAAU;QAAQ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAE3F", "debugId": null}}, {"offset": {"line": 265, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils';\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"skeleton\"\r\n      className={cn('bg-accent animate-pulse rounded-md', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Skeleton };\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/store/hooks.ts"], "sourcesContent": ["import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';\r\nimport type { RootState, AppDispatch } from './index';\r\n\r\n// Use throughout your app instead of plain `useDispatch` and `useSelector`\r\nexport const useAppDispatch = () => useDispatch<AppDispatch>();\r\nexport const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAIO,MAAM,iBAAiB,IAAM,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD;AACvC,MAAM,iBAAkD,yJAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/ProfileCompletionIndicator.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { Button } from '@/components/ui/button';\r\nimport { User, ArrowRight } from 'lucide-react';\r\nimport { useSelector } from 'react-redux';\r\nimport { RootState } from '@/store';\r\n\r\nconst ProfileCompletionIndicator = () => {\r\n  const router = useRouter();\r\n  const { profileData } = useSelector((state: RootState) => state.studentProfile);\r\n\r\n  // Check if student is logged in\r\n  const isLoggedIn = typeof window !== 'undefined' && localStorage.getItem('studentToken') !== null;\r\n\r\n  // Check if student has any profile data - only check if profile ID exists\r\n  const hasProfile = profileData?.profile?.id !== undefined;\r\n\r\n  // Only show the indicator if student is logged in and doesn't have a profile\r\n  if (!isLoggedIn || hasProfile) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className=\"my-4 mx-10 sm:px-4\">\r\n      <div className=\"bg-white dark:bg-gray-900 border border-orange-200 overflow-hidden dark:border-orange-900\">\r\n        <div className=\"px-3 py-1.5 flex items-center justify-between\">\r\n        <div className=\"flex items-center space-x-2\">\r\n          <div className=\"bg-[#ff914d] p-1.5 rounded-full\">\r\n            <User className=\"h-3 w-3 text-white\" />\r\n          </div>\r\n          <p className=\"text-xs font-medium text-gray-800 dark:text-gray-200\">\r\n            Please Complete your profile \r\n          </p>\r\n        </div>\r\n        <Button\r\n          onClick={() => router.push('/student/profile')}\r\n          className=\"bg-[#ff914d] hover:bg-[#e07c3a] text-white text-xs px-2 py-0.5 h-6 min-w-0\"\r\n          size=\"sm\"\r\n        >\r\n         Complete now <ArrowRight className=\"h-3 w-3\" />\r\n        </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProfileCompletionIndicator;\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;;AASA,MAAM,6BAA6B;IACjC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAqB,MAAM,cAAc;IAE9E,gCAAgC;IAChC,MAAM,aAAa,gBAAkB,eAAe,aAAa,OAAO,CAAC,oBAAoB;IAE7F,0EAA0E;IAC1E,MAAM,aAAa,aAAa,SAAS,OAAO;IAEhD,6EAA6E;IAC7E,wCAA+B;QAC7B,OAAO;IACT;;AAyBF;uCAEe", "debugId": null}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/popover.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as PopoverPrimitive from '@radix-ui/react-popover';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Popover({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />;\r\n}\r\n\r\nfunction PopoverTrigger({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />;\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = 'center',\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot=\"popover-content\"\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden',\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction PopoverAnchor({ ...props }: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />;\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor };\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EAAE,GAAG,OAA2D;IAC/E,qBAAO,8OAAC,mKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;AAEA,SAAS,eAAe,EAAE,GAAG,OAA8D;IACzF,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,cAAc,EAAE,GAAG,OAA6D;IACvF,qBAAO,8OAAC,mKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE", "debugId": null}}, {"offset": {"line": 406, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction AlertDialog({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Root>) {\r\n  return <AlertDialogPrimitive.Root data-slot=\"alert-dialog\" {...props} />\r\n}\r\n\r\nfunction AlertDialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Trigger>) {\r\n  return (\r\n    <AlertDialogPrimitive.Trigger data-slot=\"alert-dialog-trigger\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Portal>) {\r\n  return (\r\n    <AlertDialogPrimitive.Portal data-slot=\"alert-dialog-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Overlay>) {\r\n  return (\r\n    <AlertDialogPrimitive.Overlay\r\n      data-slot=\"alert-dialog-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Content>) {\r\n  return (\r\n    <AlertDialogPortal>\r\n      <AlertDialogOverlay />\r\n      <AlertDialogPrimitive.Content\r\n        data-slot=\"alert-dialog-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </AlertDialogPortal>\r\n  )\r\n}\r\n\r\nfunction AlertDialogHeader({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-header\"\r\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogFooter({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-footer\"\r\n      className={cn(\r\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Title>) {\r\n  return (\r\n    <AlertDialogPrimitive.Title\r\n      data-slot=\"alert-dialog-title\"\r\n      className={cn(\"text-lg font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Description>) {\r\n  return (\r\n    <AlertDialogPrimitive.Description\r\n      data-slot=\"alert-dialog-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogAction({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Action>) {\r\n  return (\r\n    <AlertDialogPrimitive.Action\r\n      className={cn(buttonVariants(), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogCancel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Cancel>) {\r\n  return (\r\n    <AlertDialogPrimitive.Cancel\r\n      className={cn(buttonVariants({ variant: \"outline\" }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGA;AAEA;AACA;AANA;;;;;AAQA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,8OAAC,2KAAA,CAAA,OAAyB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,2KAAA,CAAA,UAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,2KAAA,CAAA,SAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC,2KAAA,CAAA,UAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,2KAAA,CAAA,UAA4B;gBAC3B,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,2KAAA,CAAA,QAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,2KAAA,CAAA,cAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,2KAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IAAI;QACrD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 566, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/notificationService.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\n\r\nexport interface Notification {\r\n  id: string;\r\n  userId: string;\r\n  userType: 'STUDENT' | 'CLASS' | 'ADMIN';\r\n  type: 'STUDENT_ACCOUNT_CREATED' | 'STUDENT_PROFILE_APPROVED' | 'STUDENT_PROFILE_REJECTED' |\r\n        'STUDENT_COIN_PURCHASE' | 'STUDENT_UWHIZ_PARTICIPATION' | 'STUDENT_CHAT_MESSAGE' |\r\n        'STUDENT_STORE_PURCHASE' | 'STUDENT_STORE_ORDER_APPROVED' | 'STUDENT_STORE_ORDER_REJECTED' |\r\n        'CLASS_ACCOUNT_CREATED' | 'CLASS_PROFILE_APPROVED' | 'CLASS_PROFILE_REJECTED' |\r\n        'CLASS_COIN_PURCHASE' | 'CLASS_CHAT_MESSAGE' | 'CLASS_CONTENT_APPROVED' | 'CLASS_CONTENT_REJECTED' |\r\n        'CLASS_EDUCATION_ADDED' | 'CLASS_EXPERIENCE_ADDED' | 'CLASS_CERTIFICATE_ADDED' |\r\n        'CLASS_STORE_PURCHASE' | 'CLASS_STORE_ORDER_APPROVED' | 'CLASS_STORE_ORDER_REJECTED' |\r\n        'ADMIN_NEW_STUDENT_REGISTRATION' | 'ADMIN_NEW_CLASS_REGISTRATION' |\r\n        'ADMIN_PROFILE_REVIEW_REQUIRED' | 'ADMIN_CONTENT_REVIEW_REQUIRED' | 'ADMIN_NEW_STORE_ORDER';\r\n  title: string;\r\n  message: string;\r\n  data?: any;\r\n  isRead: boolean;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\nexport interface NotificationPagination {\r\n  currentPage: number;\r\n  totalPages: number;\r\n  totalCount: number;\r\n  limit: number;\r\n  hasNextPage: boolean;\r\n  hasPrevPage: boolean;\r\n}\r\n\r\nexport interface NotificationResponse {\r\n  notifications: Notification[];\r\n  pagination: NotificationPagination;\r\n}\r\n\r\n// For Classes (authenticated users)\r\nexport const getClassNotifications = async (page: number = 1, limit: number = 10): Promise<NotificationResponse> => {\r\n  const response = await axiosInstance.get(`/notifications/classes?page=${page}&limit=${limit}`);\r\n  return response.data.data;\r\n};\r\n\r\n\r\n\r\nexport const getClassUnreadCount = async (): Promise<number> => {\r\n  const response = await axiosInstance.get('/notifications/classes/count');\r\n  return response.data.data.count;\r\n};\r\n\r\nexport const markClassNotificationAsRead = async (notificationId: string) => {\r\n  const response = await axiosInstance.post(`/notifications/classes/mark-read/${notificationId}`);\r\n  return response.data;\r\n};\r\n\r\nexport const markAllClassNotificationsAsRead = async () => {\r\n  const response = await axiosInstance.post('/notifications/classes/mark-all-read');\r\n  return response.data;\r\n};\r\n\r\nexport const deleteAllClassNotifications = async () => {\r\n  const response = await axiosInstance.delete('/notifications/classes/delete-all');\r\n  return response.data;\r\n};\r\n\r\n// For Students (bearer token auth)\r\nexport const getStudentNotifications = async (page: number = 1, limit: number = 10): Promise<NotificationResponse> => {\r\n  const response = await axiosInstance.get(`/notifications/students?page=${page}&limit=${limit}`);\r\n  return response.data.data;\r\n};\r\n\r\n\r\n\r\nexport const getStudentUnreadCount = async (): Promise<number> => {\r\n  const response = await axiosInstance.get('/notifications/students/count');\r\n  return response.data.data.count;\r\n};\r\n\r\nexport const markStudentNotificationAsRead = async (notificationId: string) => {\r\n  const response = await axiosInstance.post(`/notifications/students/mark-read/${notificationId}`);\r\n  return response.data;\r\n};\r\n\r\nexport const markAllStudentNotificationsAsRead = async () => {\r\n  const response = await axiosInstance.post('/notifications/students/mark-all-read');\r\n  return response.data;\r\n};\r\n\r\nexport const deleteAllStudentNotifications = async () => {\r\n  const response = await axiosInstance.delete('/notifications/students/delete-all');\r\n  return response.data;\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AAsCO,MAAM,wBAAwB,OAAO,OAAe,CAAC,EAAE,QAAgB,EAAE;IAC9E,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,4BAA4B,EAAE,KAAK,OAAO,EAAE,OAAO;IAC7F,OAAO,SAAS,IAAI,CAAC,IAAI;AAC3B;AAIO,MAAM,sBAAsB;IACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;IACzC,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK;AACjC;AAEO,MAAM,8BAA8B,OAAO;IAChD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,iCAAiC,EAAE,gBAAgB;IAC9F,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,kCAAkC;IAC7C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC;IAC1C,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,8BAA8B;IACzC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;IAC5C,OAAO,SAAS,IAAI;AACtB;AAGO,MAAM,0BAA0B,OAAO,OAAe,CAAC,EAAE,QAAgB,EAAE;IAChF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,KAAK,OAAO,EAAE,OAAO;IAC9F,OAAO,SAAS,IAAI,CAAC,IAAI;AAC3B;AAIO,MAAM,wBAAwB;IACnC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;IACzC,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,KAAK;AACjC;AAEO,MAAM,gCAAgC,OAAO;IAClD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,kCAAkC,EAAE,gBAAgB;IAC/F,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,oCAAoC;IAC/C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC;IAC1C,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,gCAAgC;IAC3C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;IAC5C,OAAO,SAAS,IAAI;AACtB", "debugId": null}}, {"offset": {"line": 626, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/NotificationBell.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { Bell } from 'lucide-react';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from '@/components/ui/popover';\r\n\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog';\r\nimport {\r\n  getClassNotifications,\r\n  getClassUnreadCount,\r\n  markClassNotificationAsRead,\r\n  markAllClassNotificationsAsRead,\r\n  deleteAllClassNotifications,\r\n  getStudentNotifications,\r\n  getStudentUnreadCount,\r\n  markStudentNotificationAsRead,\r\n  markAllStudentNotificationsAsRead,\r\n  deleteAllStudentNotifications,\r\n  Notification\r\n} from '@/services/notificationService';\r\nimport { toast } from 'sonner';\r\nimport { formatDistanceToNow } from 'date-fns';\r\nimport { useRouter } from 'next/navigation';\r\n\r\ninterface NotificationBellProps {\r\n  userType: 'class' | 'student';\r\n}\r\n\r\nexport default function NotificationBell({ userType }: NotificationBellProps) {\r\n  const [notifications, setNotifications] = useState<Notification[]>([]);\r\n  const [unreadCount, setUnreadCount] = useState(0);\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [loading, setLoading] = useState(false);\r\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\r\n\r\n  const router = useRouter();\r\n\r\n  const safeNotifications = Array.isArray(notifications) ? notifications : [];\r\n\r\n  const fetchNotifications = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      let result: any;\r\n      let count: number;\r\n\r\n      if (userType === 'class') {\r\n        result = await getClassNotifications(1, 20);\r\n        count = await getClassUnreadCount();\r\n      } else {\r\n        result = await getStudentNotifications(1, 20);\r\n        count = await getStudentUnreadCount();\r\n      }\r\n\r\n      // Handle both old and new response formats\r\n      const notifs = result?.notifications || result || [];\r\n      setNotifications(Array.isArray(notifs) ? notifs : []);\r\n      setUnreadCount(count);\r\n    } catch (error) {\r\n      console.error('Error fetching notifications:', error);\r\n      setNotifications([]);\r\n      setUnreadCount(0);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [userType]);\r\n\r\n  const handleNotificationClick = async (notification: Notification) => {\r\n    try {\r\n      // Mark notification as read\r\n      if (userType === 'class') {\r\n        await markClassNotificationAsRead(notification.id);\r\n      } else {\r\n        await markStudentNotificationAsRead(notification.id);\r\n      }\r\n\r\n      // Update local state\r\n      setNotifications(prev =>\r\n        prev.map(notif =>\r\n          notif.id === notification.id ? { ...notif, isRead: true } : notif\r\n        )\r\n      );\r\n      setUnreadCount(prev => Math.max(0, prev - 1));\r\n      setIsOpen(false);\r\n      if (notification.data?.actionType === 'OPEN_CHAT' && notification.data?.redirectUrl) {\r\n        router.push(notification.data.redirectUrl);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error handling notification click:', error);\r\n      toast.error('Failed to process notification');\r\n    }\r\n  };\r\n\r\n  const handleMarkAllAsRead = async () => {\r\n    try {\r\n      if (userType === 'class') {\r\n        await markAllClassNotificationsAsRead();\r\n      } else {\r\n        await markAllStudentNotificationsAsRead();\r\n      }\r\n\r\n      // Update local state\r\n      setNotifications(prev =>\r\n        prev.map(notif => ({ ...notif, isRead: true }))\r\n      );\r\n      setUnreadCount(0);\r\n      toast.success('All notifications marked as read');\r\n    } catch (error) {\r\n      console.error('Error marking all notifications as read:', error);\r\n      toast.error('Failed to mark all notifications as read');\r\n    }\r\n  };\r\n\r\n  const handleRemoveAllClick = () => {\r\n    setShowDeleteDialog(true);\r\n  };\r\n\r\n  const handleConfirmRemoveAll = async () => {\r\n    setShowDeleteDialog(false);\r\n\r\n    try {\r\n      if (userType === 'class') {\r\n        await deleteAllClassNotifications();\r\n      } else {\r\n        await deleteAllStudentNotifications();\r\n      }\r\n\r\n      // Update local state\r\n      setNotifications([]);\r\n      setUnreadCount(0);\r\n      toast.success('All notifications removed successfully');\r\n    } catch (error) {\r\n      console.error('Error removing all notifications:', error);\r\n      toast.error('Failed to remove all notifications');\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchNotifications();\r\n\r\n    const interval = setInterval(fetchNotifications, 30000);\r\n\r\n    return () => clearInterval(interval);\r\n  }, [fetchNotifications]);\r\n\r\n  return (\r\n    <>\r\n      <Popover open={isOpen} onOpenChange={setIsOpen}>\r\n        <PopoverTrigger asChild>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"relative bg-black hover:bg-gray-900 transition duration-200 h-9 w-9 md:h-10 md:w-10 rounded-full\"\r\n          >\r\n            <div className=\"absolute inset-0 rounded-full bg-orange-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none\" />\r\n\r\n            <Bell className=\"relative z-10 h-4 w-4 md:h-5 md:w-5 text-white transition-colors duration-200\" />\r\n\r\n            {unreadCount > 0 && (\r\n              <div className=\"absolute -top-1.5 -right-1.5 md:-top-2 md:-right-2 h-5 w-5 bg-red-500 rounded-full flex items-center justify-center z-20\">\r\n                <span className=\"text-white text-[10px] md:text-xs font-semibold leading-none\">\r\n                  {unreadCount > 99 ? '99+' : unreadCount}\r\n                </span>\r\n              </div>\r\n            )}\r\n          </Button>\r\n        </PopoverTrigger>\r\n        <PopoverContent className=\"w-80 p-0\" align=\"end\">\r\n          <div className=\"p-4 border-b\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <h3 className=\"font-semibold\">Notifications</h3>\r\n              <div className=\"flex gap-2\">\r\n                {unreadCount > 0 && (\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"sm\"\r\n                    onClick={handleMarkAllAsRead}\r\n                    className=\"text-xs\"\r\n                  >\r\n                    Mark all read\r\n                  </Button>\r\n                )}\r\n                {notifications.length > 0 && unreadCount === 0 && (\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"sm\"\r\n                    onClick={handleRemoveAllClick}\r\n                    className=\"text-xs text-red-600 hover:text-red-700 hover:bg-red-50\"\r\n                  >\r\n                    Remove all\r\n                  </Button>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div className=\"h-80 overflow-y-auto\">\r\n            {loading ? (\r\n              <div className=\"p-4 text-center text-muted-foreground\">\r\n                Loading notifications...\r\n              </div>\r\n            ) : notifications.length === 0 ? (\r\n              <div className=\"p-4 text-center text-muted-foreground\">\r\n                No notifications yet\r\n              </div>\r\n            ) : (\r\n              <div className=\"divide-y\">\r\n                {Array.isArray(notifications) && notifications.map((notification) => (\r\n                  <div\r\n                    key={notification.id}\r\n                    className={`p-4 cursor-pointer hover:bg-muted/50 transition-colors ${!notification.isRead ? 'bg-blue-50/50' : ''\r\n                      }`}\r\n                    onClick={() => handleNotificationClick(notification)}\r\n                  >\r\n                    <div className=\"flex items-start gap-3\">\r\n                      <div className={`w-2 h-2 rounded-full mt-2 ${!notification.isRead ? 'bg-blue-500' : 'bg-gray-300'\r\n                        }`} />\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <p className=\"font-medium text-sm\">{notification.title}</p>\r\n                        <p className=\"text-sm text-muted-foreground mt-1\">\r\n                          {notification.message}\r\n                        </p>\r\n                        <p className=\"text-xs text-muted-foreground mt-2\">\r\n                          {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n          {safeNotifications.length > 0 && (\r\n            <div className=\"p-3 border-t bg-muted/30\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                className=\"w-full text-xs\"\r\n                onClick={() => {\r\n                  setIsOpen(false);\r\n                  router.push('/notifications');\r\n                }}\r\n              >\r\n                View All Notifications\r\n              </Button>\r\n            </div>\r\n          )}\r\n        </PopoverContent>\r\n      </Popover>\r\n\r\n      {/* Delete Confirmation Dialog */}\r\n      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Remove All Notifications</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Are you sure you want to remove all notifications? This action cannot be undone.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel>Cancel</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={handleConfirmRemoveAll}\r\n              className=\"bg-red-600 hover:bg-red-700\"\r\n            >\r\n              Remove All\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAMA;AAUA;AAaA;AACA;AACA;AApCA;;;;;;;;;;;AA0Ce,SAAS,iBAAiB,EAAE,QAAQ,EAAyB;IAC1E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,oBAAoB,MAAM,OAAO,CAAC,iBAAiB,gBAAgB,EAAE;IAE3E,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,IAAI;YACF,WAAW;YACX,IAAI;YACJ,IAAI;YAEJ,IAAI,aAAa,SAAS;gBACxB,SAAS,MAAM,CAAA,GAAA,sIAAA,CAAA,wBAAqB,AAAD,EAAE,GAAG;gBACxC,QAAQ,MAAM,CAAA,GAAA,sIAAA,CAAA,sBAAmB,AAAD;YAClC,OAAO;gBACL,SAAS,MAAM,CAAA,GAAA,sIAAA,CAAA,0BAAuB,AAAD,EAAE,GAAG;gBAC1C,QAAQ,MAAM,CAAA,GAAA,sIAAA,CAAA,wBAAqB,AAAD;YACpC;YAEA,2CAA2C;YAC3C,MAAM,SAAS,QAAQ,iBAAiB,UAAU,EAAE;YACpD,iBAAiB,MAAM,OAAO,CAAC,UAAU,SAAS,EAAE;YACpD,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,iBAAiB,EAAE;YACnB,eAAe;QACjB,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,0BAA0B,OAAO;QACrC,IAAI;YACF,4BAA4B;YAC5B,IAAI,aAAa,SAAS;gBACxB,MAAM,CAAA,GAAA,sIAAA,CAAA,8BAA2B,AAAD,EAAE,aAAa,EAAE;YACnD,OAAO;gBACL,MAAM,CAAA,GAAA,sIAAA,CAAA,gCAA6B,AAAD,EAAE,aAAa,EAAE;YACrD;YAEA,qBAAqB;YACrB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,QACP,MAAM,EAAE,KAAK,aAAa,EAAE,GAAG;wBAAE,GAAG,KAAK;wBAAE,QAAQ;oBAAK,IAAI;YAGhE,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,GAAG,OAAO;YAC1C,UAAU;YACV,IAAI,aAAa,IAAI,EAAE,eAAe,eAAe,aAAa,IAAI,EAAE,aAAa;gBACnF,OAAO,IAAI,CAAC,aAAa,IAAI,CAAC,WAAW;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI;YACF,IAAI,aAAa,SAAS;gBACxB,MAAM,CAAA,GAAA,sIAAA,CAAA,kCAA+B,AAAD;YACtC,OAAO;gBACL,MAAM,CAAA,GAAA,sIAAA,CAAA,oCAAiC,AAAD;YACxC;YAEA,qBAAqB;YACrB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,QAAS,CAAC;wBAAE,GAAG,KAAK;wBAAE,QAAQ;oBAAK,CAAC;YAE/C,eAAe;YACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,uBAAuB;QAC3B,oBAAoB;IACtB;IAEA,MAAM,yBAAyB;QAC7B,oBAAoB;QAEpB,IAAI;YACF,IAAI,aAAa,SAAS;gBACxB,MAAM,CAAA,GAAA,sIAAA,CAAA,8BAA2B,AAAD;YAClC,OAAO;gBACL,MAAM,CAAA,GAAA,sIAAA,CAAA,gCAA6B,AAAD;YACpC;YAEA,qBAAqB;YACrB,iBAAiB,EAAE;YACnB,eAAe;YACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QAEA,MAAM,WAAW,YAAY,oBAAoB;QAEjD,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAmB;IAEvB,qBACE;;0BACE,8OAAC,mIAAA,CAAA,UAAO;gBAAC,MAAM;gBAAQ,cAAc;;kCACnC,8OAAC,mIAAA,CAAA,iBAAc;wBAAC,OAAO;kCACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;;;;;8CAEf,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAEf,cAAc,mBACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;kCAMtC,8OAAC,mIAAA,CAAA,iBAAc;wBAAC,WAAU;wBAAW,OAAM;;0CACzC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAgB;;;;;;sDAC9B,8OAAC;4CAAI,WAAU;;gDACZ,cAAc,mBACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;gDAIF,cAAc,MAAM,GAAG,KAAK,gBAAgB,mBAC3C,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;0CAOT,8OAAC;gCAAI,WAAU;0CACZ,wBACC,8OAAC;oCAAI,WAAU;8CAAwC;;;;;2CAGrD,cAAc,MAAM,KAAK,kBAC3B,8OAAC;oCAAI,WAAU;8CAAwC;;;;;yDAIvD,8OAAC;oCAAI,WAAU;8CACZ,MAAM,OAAO,CAAC,kBAAkB,cAAc,GAAG,CAAC,CAAC,6BAClD,8OAAC;4CAEC,WAAW,CAAC,uDAAuD,EAAE,CAAC,aAAa,MAAM,GAAG,kBAAkB,IAC1G;4CACJ,SAAS,IAAM,wBAAwB;sDAEvC,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,0BAA0B,EAAE,CAAC,aAAa,MAAM,GAAG,gBAAgB,eAChF;;;;;;kEACJ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAuB,aAAa,KAAK;;;;;;0EACtD,8OAAC;gEAAE,WAAU;0EACV,aAAa,OAAO;;;;;;0EAEvB,8OAAC;gEAAE,WAAU;0EACV,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,aAAa,SAAS,GAAG;oEAAE,WAAW;gEAAK;;;;;;;;;;;;;;;;;;2CAd1E,aAAa,EAAE;;;;;;;;;;;;;;;4BAuB7B,kBAAkB,MAAM,GAAG,mBAC1B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;wCACP,UAAU;wCACV,OAAO,IAAI,CAAC;oCACd;8CACD;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC,2IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAkB,cAAc;0BACjD,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,8OAAC,2IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAI1B,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 1056, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/avatar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as AvatarPrimitive from '@radix-ui/react-avatar';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Avatar({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn('relative flex size-8 shrink-0 overflow-hidden rounded-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarImage({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn('aspect-square size-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn('bg-muted flex size-full items-center justify-center rounded-full', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback };\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EAAE,SAAS,EAAE,GAAG,OAA0D;IACxF,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8DAA8D;QAC3E,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC9F,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oEAAoE;QACjF,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1108, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/studentAuthServices.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\nimport { GoogleAuthData } from '@/lib/types';\r\n\r\ninterface StudentRegisterData {\r\n  firstName: string;\r\n  lastName: string;\r\n  contactNo: string;\r\n  referralCode?: string;\r\n}\r\n\r\ninterface StudentLoginData {\r\n  contactNo: string;\r\n  email?: string;\r\n}\r\n\r\ninterface VerifyOtpData {\r\n  contactNo: string;\r\n  otp: string;\r\n  firstName?: string;\r\n  lastName?: string;\r\n  email?: string;\r\n}\r\n\r\ninterface ResendOtpData {\r\n  contactNo: string;\r\n  firstName?: string;\r\n}\r\n\r\ninterface ContinueWithEmailData {\r\n  email: string;\r\n}\r\n\r\nexport const continueWithEmail = async (data: ContinueWithEmailData) => {\r\n  const response = await axiosInstance.post('/student/continue-with-email', data);\r\n  return response.data;\r\n};\r\n\r\nexport const registerStudent = async (data: StudentRegisterData) => {\r\n  const response = await axiosInstance.post('/student/register', data);\r\n  return response.data;\r\n};\r\n\r\nexport const loginStudent = async (data: StudentLoginData) => {\r\n  const response = await axiosInstance.post('/student/login', data);\r\n  return response.data;\r\n};\r\n\r\nexport const logoutStudent = async (): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.post('/student/logout');\r\n\r\n    localStorage.removeItem('studentToken');\r\n    localStorage.removeItem('student_data');\r\n\r\n    return response.data;\r\n  } catch {\r\n    localStorage.removeItem('studentToken');\r\n    localStorage.removeItem('student_data');\r\n\r\n    return {\r\n      success: true,\r\n      message: 'Logged out successfully',\r\n    };\r\n  }\r\n};\r\n\r\nexport async function verifyOtp(data: VerifyOtpData) {\r\n  const response = await axiosInstance.post('/student/verify-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function resendOtp(data: ResendOtpData) {\r\n  const response = await axiosInstance.post('/student/resend-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport const googleAuthStudent = async (googleAuthData: GoogleAuthData) => {\r\n    const response = await axiosInstance.post(`/student/google-auth`, googleAuthData);\r\n    return response.data;\r\n};\r\n\r\nexport const studentverifyEmail = async (token: string) => {\r\n    const response = await axiosInstance.post(`/student/verify-email`, { token });\r\n    return response.data;\r\n};\r\n\r\nexport const studentresendVerificationEmail = async (email: string) => {\r\n    const response = await axiosInstance.post(`/student/resend-verification`, { email });\r\n    return response.data;\r\n};"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AAgCO,MAAM,oBAAoB,OAAO;IACtC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,gCAAgC;IAC1E,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,kBAAkB,OAAO;IACpC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,qBAAqB;IAC/D,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,eAAe,OAAO;IACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,kBAAkB;IAC5D,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,gBAAgB;IAC3B,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC;QAE1C,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QAExB,OAAO,SAAS,IAAI;IACtB,EAAE,OAAM;QACN,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QAExB,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF;AACF;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB;IACjE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB;IACjE,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,oBAAoB,OAAO;IACpC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,oBAAoB,CAAC,EAAE;IAClE,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,qBAAqB,OAAO;IACrC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,qBAAqB,CAAC,EAAE;QAAE;IAAM;IAC3E,OAAO,SAAS,IAAI;AACxB;AAEO,MAAM,iCAAiC,OAAO;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,4BAA4B,CAAC,EAAE;QAAE;IAAM;IAClF,OAAO,SAAS,IAAI;AACxB", "debugId": null}}, {"offset": {"line": 1178, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/AuthService.ts"], "sourcesContent": ["import { axiosInstance } from '../lib/axios';\r\n\r\ninterface RegisterData {\r\n  firstName: string;\r\n  lastName: string;\r\n  contactNo: string;\r\n  referralCode?: string;\r\n}\r\n\r\ninterface LoginData {\r\n  contactNo: string;\r\n  email?: string;\r\n}\r\n\r\ninterface VerifyOtpData {\r\n  contactNo: string;\r\n  otp: string;\r\n  email?: string;\r\n  firstName?: string;\r\n  lastName?: string;\r\n}\r\n\r\ninterface ResendOtpData {\r\n  contactNo: string;\r\n  firstName?: string;\r\n}\r\n\r\ninterface ContinueWithEmailData {\r\n  email: string;\r\n}\r\n\r\nexport async function continueWithEmail(data: ContinueWithEmailData) {\r\n  const response = await axiosInstance.post('/auth-client/continue-with-email', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function registerUser(data: RegisterData) {\r\n  const response = await axiosInstance.post('/auth-client/register', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function loginUser(data: LoginData) {\r\n  const response = await axiosInstance.post('/auth-client/login', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function verifyOtp(data: VerifyOtpData) {\r\n  const response = await axiosInstance.post('/auth-client/verify-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport async function resendOtp(data: ResendOtpData) {\r\n  const response = await axiosInstance.post('/auth-client/resend-otp', data);\r\n  return response.data;\r\n}\r\n\r\nexport function logoutUser(): void {\r\n  localStorage.removeItem('user');\r\n}\r\n\r\nexport const generateJWT = async (contact: string | undefined, password : string | undefined) => {\r\n  const response = await axiosInstance.post(`/auth-client/generate-jwt`, { contact, password });\r\n  return response.data;\r\n};\r\n\r\n\r\n\r\nexport const verifyEmail = async (token: string) => {\r\n  const response = await axiosInstance.get(`/auth-client/verify-email`, { params: { token } });\r\n  return response.data;\r\n};\r\n\r\nexport const resendVerificationEmail = async (email: string) => {\r\n  const response = await axiosInstance.post(`/auth-client/resend-verification`, { email });\r\n  return response.data;\r\n};"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AA+BO,eAAe,kBAAkB,IAA2B;IACjE,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,oCAAoC;IAC9E,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,aAAa,IAAkB;IACnD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,yBAAyB;IACnE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAe;IAC7C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,sBAAsB;IAChE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,2BAA2B;IACrE,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,UAAU,IAAmB;IACjD,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,2BAA2B;IACrE,OAAO,SAAS,IAAI;AACtB;AAEO,SAAS;IACd,aAAa,UAAU,CAAC;AAC1B;AAEO,MAAM,cAAc,OAAO,SAA6B;IAC7D,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,yBAAyB,CAAC,EAAE;QAAE;QAAS;IAAS;IAC3F,OAAO,SAAS,IAAI;AACtB;AAIO,MAAM,cAAc,OAAO;IAChC,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,yBAAyB,CAAC,EAAE;QAAE,QAAQ;YAAE;QAAM;IAAE;IAC1F,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,0BAA0B,OAAO;IAC5C,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,CAAC,gCAAgC,CAAC,EAAE;QAAE;IAAM;IACtF,OAAO,SAAS,IAAI;AACtB", "debugId": null}}, {"offset": {"line": 1241, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/mockExamStreakApi.ts"], "sourcesContent": ["import { axiosInstance } from \"../lib/axios\";\r\n\r\nexport const saveMockExamStreak = async (studentId: string): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.put(`/mock-exam-streak/${studentId}`, {}, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return { success: true, data: response.data.data };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to save mock exam streak: ${\r\n        error.response?.data?.error || error.message\r\n      }`,\r\n    };\r\n  }\r\n};\r\n\r\nexport const getMockExamStreak = async (studentId: string): Promise<any> => {\r\n  try {\r\n    const response = await axiosInstance.get(`/mock-exam-streak/${studentId}`, {\r\n      headers: {\r\n        \"Server-Select\": \"uwhizServer\",\r\n      },\r\n    });\r\n    return { success: true, data: response.data.data }; \r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: `Failed to get mock exam streak: ${\r\n        error.response?.data?.error || error.message\r\n      }`,\r\n    };\r\n  }\r\n};"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,qBAAqB,OAAO;IACvC,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,WAAW,EAAE,CAAC,GAAG;YAC7E,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI,CAAC,IAAI;QAAC;IACnD,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,iCAAiC,EACvC,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,EAC5C;QACJ;IACF;AACF;AAEO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,WAAW,EAAE;YACzE,SAAS;gBACP,iBAAiB;YACnB;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM,SAAS,IAAI,CAAC,IAAI;QAAC;IACnD,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,CAAC,gCAAgC,EACtC,MAAM,QAAQ,EAAE,MAAM,SAAS,MAAM,OAAO,EAC5C;QACJ;IACF;AACF", "debugId": null}}, {"offset": {"line": 1289, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/streakcountdisplay.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { getMockExamStreak } from '@/services/mockExamStreakApi';\r\n\r\n\r\ninterface MockExamStreakResponse {\r\n  success: boolean;\r\n  data?: { streak: number; lastAttempt: string | null };\r\n  error?: string;\r\n}\r\n\r\ninterface StreakDisplayProps {\r\n  studentId?: string;\r\n}\r\n\r\nconst StreakDisplay: React.FC<StreakDisplayProps> = ({ studentId }) => {\r\n  const [streak, setStreak] = useState<number>(0);\r\n\r\n  useEffect(() => {\r\n    const fetchStreak = async () => {\r\n      if (!studentId) {\r\n        setStreak(0);\r\n        return;\r\n      }\r\n      const response: MockExamStreakResponse = await getMockExamStreak(studentId);\r\n      if (response.success && response.data) {\r\n        setStreak(response.data.streak || 0);\r\n      } else {\r\n        setStreak(0);\r\n      }\r\n    };\r\n    fetchStreak();\r\n  }, [studentId]);\r\n\r\n  return (\r\n       <span className=\"bg-black  text-white text-l rounded-full px-2 py-1 flex items-center gap-1\">\r\n      🔥 {streak}\r\n    </span>\r\n  );\r\n};\r\n\r\nexport default StreakDisplay;"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAaA,MAAM,gBAA8C,CAAC,EAAE,SAAS,EAAE;IAChE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,IAAI,CAAC,WAAW;gBACd,UAAU;gBACV;YACF;YACA,MAAM,WAAmC,MAAM,CAAA,GAAA,oIAAA,CAAA,oBAAiB,AAAD,EAAE;YACjE,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,UAAU,SAAS,IAAI,CAAC,MAAM,IAAI;YACpC,OAAO;gBACL,UAAU;YACZ;QACF;QACA;IACF,GAAG;QAAC;KAAU;IAEd,qBACK,8OAAC;QAAK,WAAU;;YAA6E;YAC1F;;;;;;;AAGV;uCAEe", "debugId": null}}, {"offset": {"line": 1336, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/Header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport {\r\n  Menu,\r\n  X,\r\n  User,\r\n  ShoppingBag,\r\n  Share2,\r\n  UserCircle,\r\n  LayoutDashboard,\r\n  MessageSquare,\r\n  Coins,\r\n  ShoppingBagIcon,\r\n  ShoppingCart,\r\n} from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { RootState } from \"@/store\";\r\nimport { useAppDispatch } from \"@/store/hooks\";\r\nimport { useEffect, useState, useRef } from \"react\";\r\nimport { isStudentAuthenticated, clearStudentAuthToken } from \"@/lib/utils\";\r\nimport ProfileCompletionIndicator from \"./ProfileCompletionIndicator\";\r\nimport NotificationBell from \"./NotificationBell\";\r\nimport { Avatar, AvatarFallback } from \"@/components/ui/avatar\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\nimport { toast } from \"sonner\";\r\nimport { logoutStudent } from \"@/services/studentAuthServices\";\r\nimport { clearUser } from \"@/store/slices/userSlice\";\r\nimport { clearStudentProfileData } from \"@/store/slices/studentProfileSlice\";\r\nimport { fetchStudentProfile } from \"@/store/thunks/studentProfileThunks\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { axiosInstance } from \"@/lib/axios\";\r\nimport { useMotionValue, useAnimationFrame } from \"framer-motion\";\r\nimport { generateJWT } from \"@/services/AuthService\";\r\nimport StreakDisplay from \"@/components/ui/streakcountdisplay\";\r\n\r\nconst Header = () => {\r\n  const { isAuthenticated, user } = useSelector(\r\n    (state: RootState) => state.user\r\n  );\r\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\r\n  const [isStudentLoggedIn, setIsStudentLoggedIn] = useState(false);\r\n  const [studentData, setStudentData] = useState<any>(null);\r\n  const dispatch = useAppDispatch();\r\n  const router = useRouter();\r\n\r\n  const contentRef = useRef<HTMLDivElement>(null);\r\n  const [contentWidth, setContentWidth] = useState(0);\r\n  const x = useMotionValue(0);\r\n  const speed = contentWidth / 20;\r\n\r\n  useEffect(() => {\r\n    const isLoggedIn = isStudentAuthenticated();\r\n    setIsStudentLoggedIn(isLoggedIn);\r\n\r\n    if (isLoggedIn) {\r\n      const storedData = localStorage.getItem(\"student_data\");\r\n      if (storedData) {\r\n        setStudentData(JSON.parse(storedData));\r\n      }\r\n      dispatch(fetchStudentProfile());\r\n    }\r\n\r\n    const handleStorageChange = () => {\r\n      const newLoginStatus = isStudentAuthenticated();\r\n      setIsStudentLoggedIn(newLoginStatus);\r\n      if (newLoginStatus) {\r\n        const storedData = localStorage.getItem(\"student_data\");\r\n        if (storedData) {\r\n          setStudentData(JSON.parse(storedData));\r\n        }\r\n        dispatch(fetchStudentProfile());\r\n      } else {\r\n        setStudentData(null);\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"storage\", handleStorageChange);\r\n\r\n    if (contentRef.current) {\r\n      const width = contentRef.current.getBoundingClientRect().width;\r\n      setContentWidth(width);\r\n    }\r\n\r\n    return () => {\r\n      window.removeEventListener(\"storage\", handleStorageChange);\r\n    };\r\n  }, [dispatch]);\r\n\r\n  useAnimationFrame((time, delta) => {\r\n    if (contentWidth === 0) return;\r\n    const currentX = x.get();\r\n    const deltaX = (speed * delta) / 1000;\r\n    let newX = currentX - deltaX;\r\n    if (newX <= -contentWidth) {\r\n      newX = 0;\r\n    }\r\n    x.set(newX);\r\n  });\r\n\r\n  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);\r\n\r\n  const handleStudentLogout = async () => {\r\n    try {\r\n      const response = await logoutStudent();\r\n      if (response.success !== false) {\r\n        clearStudentAuthToken();\r\n        setIsStudentLoggedIn(false);\r\n        setStudentData(null);\r\n        localStorage.removeItem(\"student_data\");\r\n        dispatch(clearStudentProfileData());\r\n        toast.success(\"Logged out successfully\");\r\n        window.dispatchEvent(new Event(\"storage\"));\r\n      } else {\r\n        toast.error(response.message || \"Failed to logout\");\r\n        clearStudentAuthToken();\r\n        setIsStudentLoggedIn(false);\r\n        setStudentData(null);\r\n        localStorage.removeItem(\"student_data\");\r\n        dispatch(clearStudentProfileData());\r\n      }\r\n    } catch (error) {\r\n      console.log(\"Failed to logout\", error);\r\n      toast.error(\"Failed to logout\");\r\n      localStorage.removeItem(\"student_data\");\r\n      clearStudentAuthToken();\r\n      setIsStudentLoggedIn(false);\r\n      setStudentData(null);\r\n      dispatch(clearStudentProfileData());\r\n    }\r\n  };\r\n\r\n  const accessClassDashboard = async () => {\r\n    try {\r\n      const response = await generateJWT(user?.contactNo, user?.password);\r\n\r\n      if (response.success) {\r\n        const { token } = response.data;\r\n        const redirectUrl = `${process.env.NEXT_PUBLIC_RANNDASS_URL}/login-class-link?uid=${user?.id}&token=${token}`;\r\n        window.location.href = redirectUrl;\r\n      } else {\r\n        toast.error(response.message || \"Failed to generate token\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Failed to generate token\", error);\r\n      toast.error(\"Failed to generate token\");\r\n    }\r\n  };\r\n\r\n  const navLinks = [\r\n    { href: \"/verified-classes\", label: \"Find Tutor\" },\r\n    { href: \"/uwhiz\", label: \"U - Whiz\" },\r\n    {\r\n      href: \"/mock-exam-card\",\r\n      label: (\r\n        <span className=\"flex items-center gap-2\">\r\n          <span>Daily Quiz</span>\r\n          {isStudentLoggedIn && <StreakDisplay studentId={studentData?.id} />}\r\n        </span>\r\n      ),\r\n      isNew: true\r\n    },\r\n    { href: \"/careers\", label: \"Career\" },\r\n    { href: \"/store\", label: \"Store\" },\r\n  ];\r\n\r\n  const classMenuItems = [\r\n    {\r\n      href: \"/classes/profile\",\r\n      icon: <UserCircle className=\"w-5 h-5 mr-2\" />,\r\n      label: \"Profile\",\r\n    },\r\n    {\r\n      href: \"/classes/chat\",\r\n      icon: <MessageSquare className=\"w-5 h-5 mr-2\" />,\r\n      label: \"Messages\",\r\n    },\r\n    {\r\n      href: \"/coins\",\r\n      icon: <Coins className=\"w-5 h-5 mr-2\" />,\r\n      label: \"Coins\",\r\n    },\r\n    {\r\n      href: \"/classes/my-orders\",\r\n      icon: <ShoppingBag className=\"w-5 h-5 mr-2\" />,\r\n      label: \"My Orders\",\r\n    },\r\n    {\r\n      onClick: accessClassDashboard,\r\n      icon: <LayoutDashboard className=\"w-5 h-5 mr-2\" />,\r\n      label: \"My Dashboard\",\r\n    },\r\n    {\r\n      href: \"/classes/referral-dashboard\",\r\n      icon: <Share2 className=\"w-5 h-5 mr-2\" />,\r\n      label: \"Referral Dashboard\",\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <>\r\n      <header className=\"sticky top-0 z-50 w-full bg-black\">\r\n        <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\r\n          <div className=\"flex h-16 items-center justify-between\">\r\n            <Link\r\n              href=\"/\"\r\n              className=\"flex items-center space-x-2 transition-transform hover:scale-105\"\r\n            >\r\n              <Image\r\n                src=\"/logo_black.png\"\r\n                alt=\"Preply Logo\"\r\n                width={120}\r\n                height={40}\r\n                className=\"rounded-sm\"\r\n              />\r\n            </Link>\r\n\r\n            <nav className=\"hidden md:flex items-center space-x-6\">\r\n              {navLinks.map((link) => (\r\n                <Link\r\n                  key={link.href}\r\n                  href={link.href}\r\n                  className=\"relative flex items-center gap-2 border border-gray-700 rounded-md px-4 py-2 text-sm font-medium text-gray-300 transition-all hover:border-orange-500 hover:text-orange-400\"\r\n                >\r\n                  {link.label}\r\n                  {link.isNew && (\r\n                    <span className=\"ml-2 text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white animate-pulse\">\r\n                      Trending\r\n                    </span>\r\n                  )}\r\n                </Link>\r\n              ))}\r\n            </nav>\r\n\r\n            <div className=\"flex items-center space-x-3\">\r\n              {isAuthenticated || isStudentLoggedIn ? (\r\n                <>\r\n                  <NotificationBell\r\n                    userType={isAuthenticated ? \"class\" : \"student\"}\r\n                  />\r\n                  <Popover>\r\n                    <PopoverTrigger asChild>\r\n                      <Avatar className=\"cursor-pointer h-9 w-9 hover:opacity-80 transition-opacity\">\r\n                        <AvatarFallback className=\"bg-white text-black flex items-center justify-center text-sm font-semibold\">\r\n                          {isAuthenticated\r\n                            ? user?.firstName && user?.lastName\r\n                              ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()\r\n                              : \"CT\"\r\n                            : studentData?.firstName && studentData?.lastName\r\n                            ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()\r\n                            : \"ST\"}\r\n                        </AvatarFallback>\r\n                      </Avatar>\r\n                    </PopoverTrigger>\r\n                    <PopoverContent className=\"w-64 bg-white p-4 rounded-lg shadow-lg\">\r\n                      <div className=\"flex items-center gap-3 mb-4\">\r\n                        <Avatar className=\"h-10 w-10\">\r\n                          <AvatarFallback className=\"bg-white text-black\">\r\n                            {isAuthenticated\r\n                              ? user?.firstName && user?.lastName\r\n                                ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()\r\n                                : \"CT\"\r\n                              : studentData?.firstName && studentData?.lastName\r\n                              ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()\r\n                              : \"ST\"}\r\n                          </AvatarFallback>\r\n                        </Avatar>\r\n                        <div>\r\n                          <p className=\"font-medium text-black\">\r\n                            {isAuthenticated\r\n                              ? user?.firstName && user?.lastName\r\n                                ? `${user.firstName} ${user.lastName}`\r\n                                : user?.className || \"Class Account\"\r\n                              : studentData?.firstName && studentData?.lastName\r\n                              ? `${studentData.firstName} ${studentData.lastName}`\r\n                              : \"Student Account\"}\r\n                          </p>\r\n                          <p className=\"text-xs text-gray-600\">\r\n                            {isAuthenticated\r\n                              ? user?.contactNo || \"<EMAIL>\"\r\n                              : studentData?.contactNo || \"<EMAIL>\"}\r\n                          </p>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div className=\"space-y-2\">\r\n                        {isAuthenticated ? (\r\n                          <>\r\n                            {classMenuItems.map((item) => (\r\n                              <Button\r\n                                asChild\r\n                                variant=\"ghost\"\r\n                                className=\"w-full justify-start px-4 py-2 hover:bg-muted rounded-md transition text-sm font-medium text-foreground\"\r\n                                key={item.href || item.label}\r\n                              >\r\n                                {item.href ? (\r\n                                  <Link\r\n                                    href={item.href}\r\n                                    className=\"flex items-center\"\r\n                                  >\r\n                                    {item.icon}\r\n                                    <span>{item.label}</span>\r\n                                  </Link>\r\n                                ) : (\r\n                                  <div\r\n                                    onClick={item.onClick}\r\n                                    className=\"flex items-center w-full\"\r\n                                  >\r\n                                    {item.icon}\r\n                                    <span>{item.label}</span>\r\n                                  </div>\r\n                                )}\r\n                              </Button>\r\n                            ))}\r\n                            <Button\r\n                              variant=\"ghost\"\r\n                              className=\"w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md\"\r\n                              onClick={async () => {\r\n                                try {\r\n                                  const response = await axiosInstance.post(\r\n                                    \"/auth-client/logout\",\r\n                                    {}\r\n                                  );\r\n                                  if (response.data.success) {\r\n                                    router.push(\"/\");\r\n                                    dispatch(clearUser());\r\n                                    localStorage.removeItem(\"token\");\r\n                                    toast.success(\"Logged out successfully\");\r\n                                  }\r\n                                } catch (error) {\r\n                                  console.error(\"Logout error:\", error);\r\n                                  toast.error(\"Failed to logout\");\r\n                                }\r\n                              }}\r\n                            >\r\n                              <User className=\"w-5 h-5 mr-2\" />\r\n                              <span>Logout</span>\r\n                            </Button>\r\n                          </>\r\n                        ) : (\r\n                          <>\r\n                            <div className=\"space-y-2\">\r\n                              {[\r\n                                {\r\n                                  href: \"/student/profile\",\r\n                                  icon: <UserCircle className=\"w-5 h-5 mr-2\" />,\r\n                                  label: \"Profile\",\r\n                                },\r\n                                {\r\n                                  href: \"/student/chat\",\r\n                                  icon: (\r\n                                    <MessageSquare className=\"w-5 h-5 mr-2\" />\r\n                                  ),\r\n                                  label: \"Messages\",\r\n                                },\r\n                                {\r\n                                  href: \"/coins\",\r\n                                  icon: <Coins className=\"w-5 h-5 mr-2\" />,\r\n                                  label: \"Coins\",\r\n                                },\r\n                                {\r\n                                  href: \"/student/wishlist\",\r\n                                  icon: (\r\n                                    <ShoppingBag className=\"w-5 h-5 mr-2\" />\r\n                                  ),\r\n                                  label: \"My Wishlist\",\r\n                                },\r\n                                {\r\n                                  href: \"/student/referral-dashboard\",\r\n                                  icon: <Share2 className=\"w-5 h-5 mr-2\" />,\r\n                                  label: \"Referral Dashboard\",\r\n                                },\r\n                                {\r\n                                  href: \"/student/my-orders\",\r\n                                  icon: (\r\n                                    <ShoppingBag className=\"w-5 h-5 mr-2\" />\r\n                                  ),\r\n                                  label: \"My Orders\",\r\n                                },\r\n                              ].map((item) => (\r\n                                <Button\r\n                                  asChild\r\n                                  variant=\"ghost\"\r\n                                  className=\"w-full justify-start px-4 py-2 hover:bg-muted rounded-md transition text-sm font-medium text-foreground\"\r\n                                  key={item.href}\r\n                                >\r\n                                  <Link\r\n                                    href={item.href}\r\n                                    className=\"flex items-center\"\r\n                                  >\r\n                                    {item.icon}\r\n                                    <span>{item.label}</span>\r\n                                  </Link>\r\n                                </Button>\r\n                              ))}\r\n                              <Button\r\n                                onClick={handleStudentLogout}\r\n                                className=\"w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md\"\r\n                              >\r\n                                <User className=\"w-5 h-5 mr-2\" />\r\n                                <span>Logout</span>\r\n                              </Button>\r\n                            </div>\r\n                          </>\r\n                        )}\r\n                      </div>\r\n                    </PopoverContent>\r\n                  </Popover>\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <div className=\"hidden md:flex items-center gap-2\">\r\n                    <Button\r\n                      className=\"bg-[#ff914d] hover:bg-[#E88143] text-white text-sm px-4 py-2 rounded-md\"\r\n                      asChild\r\n                    >\r\n                      <Link href=\"/class/login\">Join as Tutor</Link>\r\n                    </Button>\r\n\r\n                    <Button\r\n                      variant=\"ghost\"\r\n                      className=\"bg-black text-white text-sm px-4 py-2 rounded-md border border-gray-700\"\r\n                      asChild\r\n                    >\r\n                      <Link href=\"/student/login\">Student Login</Link>\r\n                    </Button>\r\n                  </div>\r\n                </>\r\n              )}\r\n\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"md:hidden text-orange-400 hover:bg-orange-500/10 rounded-full\"\r\n                onClick={toggleMenu}\r\n              >\r\n                {isMenuOpen ? (\r\n                  <X className=\"h-6 w-6\" />\r\n                ) : (\r\n                  <Menu className=\"h-6 w-6\" />\r\n                )}\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </header>\r\n\r\n      <div>\r\n        <div\r\n          className={`fixed inset-y-0 right-0 z-50 w-80 bg-black transform transition-all duration-300 ease-in-out md:hidden ${\r\n            isMenuOpen ? \"translate-x-0\" : \"translate-x-full\"\r\n          }`}\r\n        >\r\n          <div className=\"flex flex-col h-full p-6\">\r\n            <div className=\"flex items-center justify-between mb-6\">\r\n              <Image\r\n                src=\"/logo_black.png\"\r\n                alt=\"Uest Logo\"\r\n                width={100}\r\n                height={32}\r\n                className=\"rounded-sm\"\r\n              />\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"text-orange-400 hover:bg-orange-500/10 rounded-full\"\r\n                onClick={toggleMenu}\r\n              >\r\n                <X className=\"h-6 w-6\" />\r\n              </Button>\r\n            </div>\r\n\r\n            {(isAuthenticated || isStudentLoggedIn) && (\r\n              <div className=\"mb-6\">\r\n                <div className=\"flex items-center gap-3 p-3 bg-gray-900 rounded-lg\">\r\n                  <Avatar className=\"h-10 w-10\">\r\n                    <AvatarFallback className=\"bg-white text-black\">\r\n                      {isAuthenticated\r\n                        ? user?.firstName && user?.lastName\r\n                          ? `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()\r\n                          : \"CT\"\r\n                        : studentData?.firstName && studentData?.lastName\r\n                        ? `${studentData.firstName[0]}${studentData.lastName[0]}`.toUpperCase()\r\n                        : \"ST\"}\r\n                    </AvatarFallback>\r\n                  </Avatar>\r\n                  <div>\r\n                    <p className=\"font-medium text-white\">\r\n                      {isAuthenticated\r\n                        ? user?.firstName && user?.lastName\r\n                          ? `${user.firstName} ${user.lastName}`\r\n                          : user?.className || \"Class Account\"\r\n                        : studentData?.firstName && studentData?.lastName\r\n                        ? `${studentData.firstName} ${studentData.lastName}`\r\n                        : \"Student Account\"}\r\n                    </p>\r\n                    <p className=\"text-xs text-gray-400\">\r\n                      {isAuthenticated\r\n                        ? user?.contactNo || \"<EMAIL>\"\r\n                        : studentData?.contactNo || \"<EMAIL>\"}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            <nav className=\"flex flex-col space-y-2\">\r\n              {navLinks.map((link) => (\r\n                <Link\r\n                  key={link.href}\r\n                  href={link.href}\r\n                  className=\"flex items-center justify-between gap-3 px-4 py-3 text-base font-medium text-gray-300 hover:text-orange-400 hover:bg-gray-900 rounded-md border border-gray-700 transition-colors\"\r\n                  onClick={toggleMenu}\r\n                >\r\n                  <div className=\"flex items-center gap-3\">\r\n                    {typeof link.label === \"string\" ? (\r\n                      <span>{link.label}</span>\r\n                    ) : (\r\n                      link.label\r\n                    )}\r\n                  </div>\r\n                  {link.isNew && (\r\n                    <span className=\"text-xs px-2 py-0.5 rounded-full bg-orange-500 text-white\">\r\n                      Trending\r\n                    </span>\r\n                  )}\r\n                </Link>\r\n              ))}\r\n            </nav>\r\n\r\n            <div className=\"mt-auto space-y-2\">\r\n              {isAuthenticated && (\r\n                <>\r\n                  {classMenuItems.map((item) => (\r\n                    <Button\r\n                      asChild\r\n                      variant=\"ghost\"\r\n                      className=\"w-full justify-start px-4 py-2 hover:bg-muted rounded-md transition text-sm font-medium text-foreground\"\r\n                      key={item.href || item.label}\r\n                    >\r\n                      {item.href ? (\r\n                        <Link\r\n                          href={item.href}\r\n                          className=\"flex items-center\"\r\n                          onClick={toggleMenu}\r\n                        >\r\n                          {item.icon}\r\n                          <span>{item.label}</span>\r\n                        </Link>\r\n                      ) : (\r\n                        <div\r\n                          onClick={() => {\r\n                            toggleMenu();\r\n                          }}\r\n                          className=\"flex items-center w-full\"\r\n                        >\r\n                          {item.icon}\r\n                          <span>{item.label}</span>\r\n                        </div>\r\n                      )}\r\n                    </Button>\r\n                  ))}\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    className=\"w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md\"\r\n                    onClick={async () => {\r\n                      try {\r\n                        const response = await axiosInstance.post(\r\n                          \"/auth-client/logout\",\r\n                          {}\r\n                        );\r\n                        if (response.data.success) {\r\n                          router.push(\"/\");\r\n                          dispatch(clearUser());\r\n                          localStorage.removeItem(\"token\");\r\n                          toast.success(\"Logged out successfully\");\r\n                        }\r\n                      } catch (error) {\r\n                        console.error(\"Logout error:\", error);\r\n                        toast.error(\"Failed to logout\");\r\n                      }\r\n                      toggleMenu();\r\n                    }}\r\n                  >\r\n                    <User className=\"w-5 h-5 mr-2\" />\r\n                    <span>Logout</span>\r\n                  </Button>\r\n                </>\r\n              )}\r\n\r\n              {isStudentLoggedIn && (\r\n                <>\r\n                  {[\r\n                    {\r\n                      href: \"/student/profile\",\r\n                      icon: <UserCircle className=\"w-5 h-5 mr-2\" />,\r\n                      label: \"Profile\",\r\n                    },\r\n                    {\r\n                      href: \"/student/chat\",\r\n                      icon: <MessageSquare className=\"w-5 h-5 mr-2\" />,\r\n                      label: \"Messages\",\r\n                    },\r\n                    {\r\n                      href: \"/coins\",\r\n                      icon: <Coins className=\"w-5 h-5 mr-2\" />,\r\n                      label: \"Coins\",\r\n                    },\r\n                    {\r\n                      href: \"/student/wishlist\",\r\n                      icon: <ShoppingBag className=\"w-5 h-5 mr-2\" />,\r\n                      label: \"My Wishlist\",\r\n                    },\r\n                    {\r\n                      href: \"/student/referral-dashboard\",\r\n                      icon: <Share2 className=\"w-5 h-5 mr-2\" />,\r\n                      label: \"Referral Dashboard\",\r\n                    },\r\n                    {\r\n                      href: \"/student/my-orders\",\r\n                      icon: <ShoppingBag className=\"w-5 h-5 mr-2\" />,\r\n                      label: \"My Orders\",\r\n                    },\r\n                  ].map((item) => (\r\n                    <Button\r\n                      asChild\r\n                      variant=\"ghost\"\r\n                      className=\"w-full justify-start px-4 py-2 hover:bg-muted rounded-md transition text-sm font-medium text-foreground \"\r\n                      key={item.href}\r\n                    >\r\n                      <Link\r\n                        href={item.href}\r\n                        className=\"flex items-center\"\r\n                        onClick={toggleMenu}\r\n                      >\r\n                        {item.icon}\r\n                        <span>{item.label}</span>\r\n                      </Link>\r\n                    </Button>\r\n                  ))}\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    className=\"w-full bg-black text-white hover:bg-[#ff914d]/10 rounded-md\"\r\n                    onClick={() => {\r\n                      handleStudentLogout();\r\n                      toggleMenu();\r\n                    }}\r\n                  >\r\n                    <User className=\"w-5 h-5 mr-2\" />\r\n                    <span>Logout</span>\r\n                  </Button>\r\n                </>\r\n              )}\r\n\r\n              {!isAuthenticated && !isStudentLoggedIn && (\r\n                <div className=\"space-y-2\">\r\n                  <Button\r\n                    className=\"w-full bg-[#ff914d] hover:bg-[#E88143] text-white rounded-lg py-3\"\r\n                    asChild\r\n                  >\r\n                    <Link href=\"/class/login\" onClick={toggleMenu}>\r\n                      Tutor Login\r\n                    </Link>\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    className=\"w-full text-[#ff914d] hover:bg-gray-900 rounded-lg py-3 border border-gray-700\"\r\n                    asChild\r\n                  >\r\n                    <Link href=\"/student/login\" onClick={toggleMenu}>\r\n                      Student Login\r\n                    </Link>\r\n                  </Button>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {isStudentLoggedIn && <ProfileCompletionIndicator />}\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAxCA;;;;;;;;;;;;;;;;;;;;;;;;AA0CA,MAAM,SAAS;IACb,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAC1C,CAAC,QAAqB,MAAM,IAAI;IAElC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,WAAW,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAC9B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,IAAI,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IACzB,MAAM,QAAQ,eAAe;IAE7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,CAAA,GAAA,mHAAA,CAAA,yBAAsB,AAAD;QACxC,qBAAqB;QAErB,IAAI,YAAY;YACd,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,YAAY;gBACd,eAAe,KAAK,KAAK,CAAC;YAC5B;YACA,SAAS,CAAA,GAAA,8IAAA,CAAA,sBAAmB,AAAD;QAC7B;QAEA,MAAM,sBAAsB;YAC1B,MAAM,iBAAiB,CAAA,GAAA,mHAAA,CAAA,yBAAsB,AAAD;YAC5C,qBAAqB;YACrB,IAAI,gBAAgB;gBAClB,MAAM,aAAa,aAAa,OAAO,CAAC;gBACxC,IAAI,YAAY;oBACd,eAAe,KAAK,KAAK,CAAC;gBAC5B;gBACA,SAAS,CAAA,GAAA,8IAAA,CAAA,sBAAmB,AAAD;YAC7B,OAAO;gBACL,eAAe;YACjB;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QAEnC,IAAI,WAAW,OAAO,EAAE;YACtB,MAAM,QAAQ,WAAW,OAAO,CAAC,qBAAqB,GAAG,KAAK;YAC9D,gBAAgB;QAClB;QAEA,OAAO;YACL,OAAO,mBAAmB,CAAC,WAAW;QACxC;IACF,GAAG;QAAC;KAAS;IAEb,CAAA,GAAA,qLAAA,CAAA,oBAAiB,AAAD,EAAE,CAAC,MAAM;QACvB,IAAI,iBAAiB,GAAG;QACxB,MAAM,WAAW,EAAE,GAAG;QACtB,MAAM,SAAS,AAAC,QAAQ,QAAS;QACjC,IAAI,OAAO,WAAW;QACtB,IAAI,QAAQ,CAAC,cAAc;YACzB,OAAO;QACT;QACA,EAAE,GAAG,CAAC;IACR;IAEA,MAAM,aAAa,IAAM,cAAc,CAAC;IAExC,MAAM,sBAAsB;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD;YACnC,IAAI,SAAS,OAAO,KAAK,OAAO;gBAC9B,CAAA,GAAA,mHAAA,CAAA,wBAAqB,AAAD;gBACpB,qBAAqB;gBACrB,eAAe;gBACf,aAAa,UAAU,CAAC;gBACxB,SAAS,CAAA,GAAA,6IAAA,CAAA,0BAAuB,AAAD;gBAC/B,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,OAAO,aAAa,CAAC,IAAI,MAAM;YACjC,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,OAAO,IAAI;gBAChC,CAAA,GAAA,mHAAA,CAAA,wBAAqB,AAAD;gBACpB,qBAAqB;gBACrB,eAAe;gBACf,aAAa,UAAU,CAAC;gBACxB,SAAS,CAAA,GAAA,6IAAA,CAAA,0BAAuB,AAAD;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,oBAAoB;YAChC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,aAAa,UAAU,CAAC;YACxB,CAAA,GAAA,mHAAA,CAAA,wBAAqB,AAAD;YACpB,qBAAqB;YACrB,eAAe;YACf,SAAS,CAAA,GAAA,6IAAA,CAAA,0BAAuB,AAAD;QACjC;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD,EAAE,MAAM,WAAW,MAAM;YAE1D,IAAI,SAAS,OAAO,EAAE;gBACpB,MAAM,EAAE,KAAK,EAAE,GAAG,SAAS,IAAI;gBAC/B,MAAM,cAAc,6DAAwC,sBAAsB,EAAE,MAAM,GAAG,OAAO,EAAE,OAAO;gBAC7G,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS,OAAO,IAAI;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,WAAW;QACf;YAAE,MAAM;YAAqB,OAAO;QAAa;QACjD;YAAE,MAAM;YAAU,OAAO;QAAW;QACpC;YACE,MAAM;YACN,qBACE,8OAAC;gBAAK,WAAU;;kCACd,8OAAC;kCAAK;;;;;;oBACL,mCAAqB,8OAAC,8IAAA,CAAA,UAAa;wBAAC,WAAW,aAAa;;;;;;;;;;;;YAGjE,OAAO;QACT;QACA;YAAE,MAAM;YAAY,OAAO;QAAS;QACpC;YAAE,MAAM;YAAU,OAAO;QAAQ;KAClC;IAED,MAAM,iBAAiB;QACrB;YACE,MAAM;YACN,oBAAM,8OAAC,kNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;YAC5B,OAAO;QACT;QACA;YACE,MAAM;YACN,oBAAM,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;YAC/B,OAAO;QACT;QACA;YACE,MAAM;YACN,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;QACT;QACA;YACE,MAAM;YACN,oBAAM,8OAAC,oNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YAC7B,OAAO;QACT;QACA;YACE,SAAS;YACT,oBAAM,8OAAC,4NAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YACjC,OAAO;QACT;QACA;YACE,MAAM;YACN,oBAAM,8OAAC,0MAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;QACT;KACD;IAED,qBACE;;0BACE,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CAEV,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;;;;;;0CAId,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;;4CAET,KAAK,KAAK;4CACV,KAAK,KAAK,kBACT,8OAAC;gDAAK,WAAU;0DAA+E;;;;;;;uCAN5F,KAAK,IAAI;;;;;;;;;;0CAcpB,8OAAC;gCAAI,WAAU;;oCACZ,mBAAmB,kCAClB;;0DACE,8OAAC,6IAAA,CAAA,UAAgB;gDACf,UAAU,kBAAkB,UAAU;;;;;;0DAExC,8OAAC,mIAAA,CAAA,UAAO;;kEACN,8OAAC,mIAAA,CAAA,iBAAc;wDAAC,OAAO;kEACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DAAC,WAAU;sEAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;gEAAC,WAAU;0EACvB,kBACG,MAAM,aAAa,MAAM,WACvB,GAAG,KAAK,SAAS,CAAC,EAAE,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACrD,OACF,aAAa,aAAa,aAAa,WACvC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACnE;;;;;;;;;;;;;;;;kEAIV,8OAAC,mIAAA,CAAA,iBAAc;wDAAC,WAAU;;0EACxB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kIAAA,CAAA,SAAM;wEAAC,WAAU;kFAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;4EAAC,WAAU;sFACvB,kBACG,MAAM,aAAa,MAAM,WACvB,GAAG,KAAK,SAAS,CAAC,EAAE,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACrD,OACF,aAAa,aAAa,aAAa,WACvC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACnE;;;;;;;;;;;kFAGR,8OAAC;;0FACC,8OAAC;gFAAE,WAAU;0FACV,kBACG,MAAM,aAAa,MAAM,WACvB,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,GACpC,MAAM,aAAa,kBACrB,aAAa,aAAa,aAAa,WACvC,GAAG,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,EAAE,GAClD;;;;;;0FAEN,8OAAC;gFAAE,WAAU;0FACV,kBACG,MAAM,aAAa,sBACnB,aAAa,aAAa;;;;;;;;;;;;;;;;;;0EAKpC,8OAAC;gEAAI,WAAU;0EACZ,gCACC;;wEACG,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,kIAAA,CAAA,SAAM;gFACL,OAAO;gFACP,SAAQ;gFACR,WAAU;0FAGT,KAAK,IAAI,iBACR,8OAAC,4JAAA,CAAA,UAAI;oFACH,MAAM,KAAK,IAAI;oFACf,WAAU;;wFAET,KAAK,IAAI;sGACV,8OAAC;sGAAM,KAAK,KAAK;;;;;;;;;;;yGAGnB,8OAAC;oFACC,SAAS,KAAK,OAAO;oFACrB,WAAU;;wFAET,KAAK,IAAI;sGACV,8OAAC;sGAAM,KAAK,KAAK;;;;;;;;;;;;+EAhBhB,KAAK,IAAI,IAAI,KAAK,KAAK;;;;;sFAqBhC,8OAAC,kIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,WAAU;4EACV,SAAS;gFACP,IAAI;oFACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CACvC,uBACA,CAAC;oFAEH,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;wFACzB,OAAO,IAAI,CAAC;wFACZ,SAAS,CAAA,GAAA,mIAAA,CAAA,YAAS,AAAD;wFACjB,aAAa,UAAU,CAAC;wFACxB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oFAChB;gFACF,EAAE,OAAO,OAAO;oFACd,QAAQ,KAAK,CAAC,iBAAiB;oFAC/B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gFACd;4EACF;;8FAEA,8OAAC,kMAAA,CAAA,OAAI;oFAAC,WAAU;;;;;;8FAChB,8OAAC;8FAAK;;;;;;;;;;;;;iGAIV;8EACE,cAAA,8OAAC;wEAAI,WAAU;;4EACZ;gFACC;oFACE,MAAM;oFACN,oBAAM,8OAAC,kNAAA,CAAA,aAAU;wFAAC,WAAU;;;;;;oFAC5B,OAAO;gFACT;gFACA;oFACE,MAAM;oFACN,oBACE,8OAAC,wNAAA,CAAA,gBAAa;wFAAC,WAAU;;;;;;oFAE3B,OAAO;gFACT;gFACA;oFACE,MAAM;oFACN,oBAAM,8OAAC,oMAAA,CAAA,QAAK;wFAAC,WAAU;;;;;;oFACvB,OAAO;gFACT;gFACA;oFACE,MAAM;oFACN,oBACE,8OAAC,oNAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;oFAEzB,OAAO;gFACT;gFACA;oFACE,MAAM;oFACN,oBAAM,8OAAC,0MAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;oFACxB,OAAO;gFACT;gFACA;oFACE,MAAM;oFACN,oBACE,8OAAC,oNAAA,CAAA,cAAW;wFAAC,WAAU;;;;;;oFAEzB,OAAO;gFACT;6EACD,CAAC,GAAG,CAAC,CAAC,qBACL,8OAAC,kIAAA,CAAA,SAAM;oFACL,OAAO;oFACP,SAAQ;oFACR,WAAU;8FAGV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wFACH,MAAM,KAAK,IAAI;wFACf,WAAU;;4FAET,KAAK,IAAI;0GACV,8OAAC;0GAAM,KAAK,KAAK;;;;;;;;;;;;mFAPd,KAAK,IAAI;;;;;0FAWlB,8OAAC,kIAAA,CAAA,SAAM;gFACL,SAAS;gFACT,WAAU;;kGAEV,8OAAC,kMAAA,CAAA,OAAI;wFAAC,WAAU;;;;;;kGAChB,8OAAC;kGAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;qEAUtB;kDACE,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,WAAU;oDACV,OAAO;8DAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;kEAAe;;;;;;;;;;;8DAG5B,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,OAAO;8DAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;kEAAiB;;;;;;;;;;;;;;;;;;kDAMpC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;kDAER,2BACC,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;iEAEb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5B,8OAAC;;kCACC,8OAAC;wBACC,WAAW,CAAC,uGAAuG,EACjH,aAAa,kBAAkB,oBAC/B;kCAEF,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;sDAEZ,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS;sDAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;gCAIhB,CAAC,mBAAmB,iBAAiB,mBACpC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;0DAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;oDAAC,WAAU;8DACvB,kBACG,MAAM,aAAa,MAAM,WACvB,GAAG,KAAK,SAAS,CAAC,EAAE,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACrD,OACF,aAAa,aAAa,aAAa,WACvC,GAAG,YAAY,SAAS,CAAC,EAAE,GAAG,YAAY,QAAQ,CAAC,EAAE,EAAE,CAAC,WAAW,KACnE;;;;;;;;;;;0DAGR,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEACV,kBACG,MAAM,aAAa,MAAM,WACvB,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,GACpC,MAAM,aAAa,kBACrB,aAAa,aAAa,aAAa,WACvC,GAAG,YAAY,SAAS,CAAC,CAAC,EAAE,YAAY,QAAQ,EAAE,GAClD;;;;;;kEAEN,8OAAC;wDAAE,WAAU;kEACV,kBACG,MAAM,aAAa,sBACnB,aAAa,aAAa;;;;;;;;;;;;;;;;;;;;;;;8CAOxC,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,SAAS;;8DAET,8OAAC;oDAAI,WAAU;8DACZ,OAAO,KAAK,KAAK,KAAK,yBACrB,8OAAC;kEAAM,KAAK,KAAK;;;;;+DAEjB,KAAK,KAAK;;;;;;gDAGb,KAAK,KAAK,kBACT,8OAAC;oDAAK,WAAU;8DAA4D;;;;;;;2CAbzE,KAAK,IAAI;;;;;;;;;;8CAqBpB,8OAAC;oCAAI,WAAU;;wCACZ,iCACC;;gDACG,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,kIAAA,CAAA,SAAM;wDACL,OAAO;wDACP,SAAQ;wDACR,WAAU;kEAGT,KAAK,IAAI,iBACR,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,KAAK,IAAI;4DACf,WAAU;4DACV,SAAS;;gEAER,KAAK,IAAI;8EACV,8OAAC;8EAAM,KAAK,KAAK;;;;;;;;;;;iFAGnB,8OAAC;4DACC,SAAS;gEACP;4DACF;4DACA,WAAU;;gEAET,KAAK,IAAI;8EACV,8OAAC;8EAAM,KAAK,KAAK;;;;;;;;;;;;uDAnBhB,KAAK,IAAI,IAAI,KAAK,KAAK;;;;;8DAwBhC,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS;wDACP,IAAI;4DACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CACvC,uBACA,CAAC;4DAEH,IAAI,SAAS,IAAI,CAAC,OAAO,EAAE;gEACzB,OAAO,IAAI,CAAC;gEACZ,SAAS,CAAA,GAAA,mIAAA,CAAA,YAAS,AAAD;gEACjB,aAAa,UAAU,CAAC;gEACxB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;4DAChB;wDACF,EAAE,OAAO,OAAO;4DACd,QAAQ,KAAK,CAAC,iBAAiB;4DAC/B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wDACd;wDACA;oDACF;;sEAEA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;sEAAK;;;;;;;;;;;;;;wCAKX,mCACC;;gDACG;oDACC;wDACE,MAAM;wDACN,oBAAM,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;wDAC5B,OAAO;oDACT;oDACA;wDACE,MAAM;wDACN,oBAAM,8OAAC,wNAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;wDAC/B,OAAO;oDACT;oDACA;wDACE,MAAM;wDACN,oBAAM,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDACvB,OAAO;oDACT;oDACA;wDACE,MAAM;wDACN,oBAAM,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDAC7B,OAAO;oDACT;oDACA;wDACE,MAAM;wDACN,oBAAM,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDACxB,OAAO;oDACT;oDACA;wDACE,MAAM;wDACN,oBAAM,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;wDAC7B,OAAO;oDACT;iDACD,CAAC,GAAG,CAAC,CAAC,qBACL,8OAAC,kIAAA,CAAA,SAAM;wDACL,OAAO;wDACP,SAAQ;wDACR,WAAU;kEAGV,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,KAAK,IAAI;4DACf,WAAU;4DACV,SAAS;;gEAER,KAAK,IAAI;8EACV,8OAAC;8EAAM,KAAK,KAAK;;;;;;;;;;;;uDARd,KAAK,IAAI;;;;;8DAYlB,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS;wDACP;wDACA;oDACF;;sEAEA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;sEAAK;;;;;;;;;;;;;;wCAKX,CAAC,mBAAmB,CAAC,mCACpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,WAAU;oDACV,OAAO;8DAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAe,SAAS;kEAAY;;;;;;;;;;;8DAIjD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,OAAO;8DAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAiB,SAAS;kEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAU5D,mCAAqB,8OAAC,uJAAA,CAAA,UAA0B;;;;;;;;;;;;;AAIzD;uCAEe", "debugId": null}}, {"offset": {"line": 2513, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app-components/Footer.tsx"], "sourcesContent": ["'use client';\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport {\r\n  FaFacebookF,\r\n  FaTwitter,\r\n  FaInstagram,\r\n  FaLinkedinIn,\r\n  FaTumblr,\r\n  FaPinterestP,\r\n  FaEnvelope,\r\n} from 'react-icons/fa';\r\n\r\nconst Footer = () => {\r\n  return (\r\n    <footer className=\"bg-black text-gray-300 px-6 py-16\">\r\n      <div className=\"container mx-auto max-w-7xl space-y-16\">\r\n        <div className=\"flex flex-col md:flex-row items-center justify-between gap-6\">\r\n          <Link href=\"/\" className=\"flex items-center gap-2\">\r\n            <Image\r\n              src=\"/logo_black.png\"\r\n              alt=\"Logo\"\r\n              width={200}\r\n              height={40}\r\n              className=\"object-contain\"\r\n            />\r\n          </Link>\r\n\r\n          <div className=\"flex flex-wrap justify-center gap-1\">\r\n            {[\r\n              { href: 'mailto:<EMAIL>', icon: FaEnvelope, label: 'Email Us' },\r\n              {\r\n                href: 'https://x.com/uest189161?t=hLD2wWnt_Zf5b5rTnkSl2Q&s=09',\r\n                icon: FaTwitter,\r\n                label: 'Twitter',\r\n              },\r\n              {\r\n                href: 'https://www.facebook.com/share/1FNYcyqawH/',\r\n                icon: FaFacebookF,\r\n                label: 'Facebook',\r\n              },\r\n              {\r\n                href: 'https://www.instagram.com/uest_edtech?igsh=MWljYWt5YnQyeW9kdg==',\r\n                icon: FaInstagram,\r\n                label: 'Instagram',\r\n              },\r\n              {\r\n                href: 'https://www.linkedin.com/company/uest-edtech/',\r\n                icon: FaLinkedinIn,\r\n                label: 'LinkedIn',\r\n              },\r\n              { href: 'https://pin.it/1Di0EFtAa', icon: FaPinterestP, label: 'Pinterest' },\r\n              {\r\n                href: 'https://www.tumblr.com/uestedtech?source=share',\r\n                icon: FaTumblr,\r\n                label: 'Tumblr',\r\n              },\r\n            ].map(({ href, icon: Icon, label }) => (\r\n              <div key={label} className=\"flex flex-col items-center\">\r\n                <Link\r\n                  href={href}\r\n                  className=\"flex items-center justify-center w-12 h-12 hover:border-gray-400 transition\"\r\n                  title={label}\r\n                >\r\n                  <Icon className=\"text-xl text-white hover:text-gray-400 transition\" />\r\n                </Link>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10\">\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">About</h3>\r\n            <ul className=\"space-y-2 text-sm\">\r\n              <li>\r\n                <Link href=\"/verified-classes\" className=\"hover:text-white transition\">\r\n                  Find Tutors\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/support\" className=\"hover:text-white transition\">\r\n                  Support\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/careers\" className=\"hover:text-white transition\">\r\n                  Careers\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">For Students</h3>\r\n            <ul className=\"space-y-2 text-sm\">\r\n              <li>\r\n                <Link href=\"/student/login\" className=\"hover:text-white transition\">\r\n                  Student Login\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/verified-classes\" className=\"hover:text-white transition\">\r\n                  Find Online Tutor\r\n                </Link>\r\n              </li>\r\n              <li>\r\n                <Link href=\"/uwhiz\" className=\"hover:text-white transition\">\r\n                  Uwhiz\r\n                </Link>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">Contact</h3>\r\n            <address className=\"not-italic text-sm space-y-1 leading-relaxed\">\r\n              <p>Head Office</p>\r\n              <p>4th Floor, Above Plus Fitness, Near Umiya Circle, Morbi – 363641</p>\r\n              <p>Contact: +91 96 877 877 88</p>\r\n              <p>Email: <EMAIL></p>\r\n            </address>\r\n          </div>\r\n\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-white mb-4\">Apps</h3>\r\n           <Link href=\"https://play.google.com/store/apps/details?id=com.uest\" target=\"_blank\">\r\n            <Image\r\n              src=\"/playstore.png\"\r\n              alt=\"Google Play Store\"\r\n              width={180}\r\n              height={50}\r\n              className=\"object-contain\"\r\n            />\r\n          </Link>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Bottom Bar */}\r\n        <div className=\"border-t border-gray-800 pt-6 text-sm flex flex-col md:flex-row justify-between items-center gap-4\">\r\n          <p>© 2025 uest.in. All rights reserved.</p>\r\n          <div className=\"flex gap-4\">\r\n            <Link href=\"/terms-and-conditions\" className=\"hover:text-white transition\">\r\n              Terms & Conditions\r\n            </Link>\r\n            <Link href=\"/privacy-policy\" className=\"hover:text-white transition\">\r\n              Privacy Policy\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </footer>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAcA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;;;;;;;;;;;sCAId,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,MAAM;oCAA8B,MAAM,8IAAA,CAAA,aAAU;oCAAE,OAAO;gCAAW;gCAC1E;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,YAAS;oCACf,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,cAAW;oCACjB,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,cAAW;oCACjB,OAAO;gCACT;gCACA;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,eAAY;oCAClB,OAAO;gCACT;gCACA;oCAAE,MAAM;oCAA4B,MAAM,8IAAA,CAAA,eAAY;oCAAE,OAAO;gCAAY;gCAC3E;oCACE,MAAM;oCACN,MAAM,8IAAA,CAAA,WAAQ;oCACd,OAAO;gCACT;6BACD,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,iBAChC,8OAAC;oCAAgB,WAAU;8CACzB,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM;wCACN,WAAU;wCACV,OAAO;kDAEP,cAAA,8OAAC;4CAAK,WAAU;;;;;;;;;;;mCANV;;;;;;;;;;;;;;;;8BAahB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAA8B;;;;;;;;;;;sDAIzE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA8B;;;;;;;;;;;sDAIhE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;sCAOpE,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAiB,WAAU;0DAA8B;;;;;;;;;;;sDAItE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAA8B;;;;;;;;;;;sDAIzE,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;sCAOlE,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAQ,WAAU;;sDACjB,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;sCAIP,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACvD,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAyD,QAAO;8CAC1E,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;;;;;;;;;;;;;;;;;;8BAOhB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCAAE;;;;;;sCACH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAwB,WAAU;8CAA8B;;;;;;8CAG3E,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAkB,WAAU;8CAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjF;uCAEe", "debugId": null}}, {"offset": {"line": 2924, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/storeApi.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\nimport { StoreFilters } from '@/lib/types';\r\n\r\nexport const getAllStoreItems = async (filters?: StoreFilters) => {\r\n  try {\r\n    const params = new URLSearchParams();\r\n    if (filters?.category) params.append('category', filters.category);\r\n    if (filters?.status) params.append('status', filters.status);\r\n    if (filters?.search) params.append('search', filters.search);\r\n\r\n    params.append('status', 'ACTIVE');\r\n\r\n    const response = await axiosInstance.get(`/admin/store?${params.toString()}`);\r\n    return {\r\n      success: true,\r\n      data: response.data.data\r\n    };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.message || error.message || 'Failed to fetch store items'\r\n    };\r\n  }\r\n};\r\n\r\nexport const getStoreItemById = async (id: string) => {\r\n  try {\r\n    const response = await axiosInstance.get(`/admin/store/${id}`);\r\n    return {\r\n      success: true,\r\n      data: response.data.data\r\n    };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.message || error.message || 'Failed to fetch store item'\r\n    };\r\n  }\r\n};\r\n\r\nexport const getStoreStats = async () => {\r\n  try {\r\n    const response = await axiosInstance.get('/admin/store/stats');\r\n    return {\r\n      success: true,\r\n      data: response.data.data\r\n    };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.message || error.message || 'Failed to fetch store statistics'\r\n    };\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;AAAA;;AAGO,MAAM,mBAAmB,OAAO;IACrC,IAAI;QACF,MAAM,SAAS,IAAI;QACnB,IAAI,SAAS,UAAU,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;QACjE,IAAI,SAAS,QAAQ,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;QAC3D,IAAI,SAAS,QAAQ,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;QAE3D,OAAO,MAAM,CAAC,UAAU;QAExB,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,OAAO,QAAQ,IAAI;QAC5E,OAAO;YACL,SAAS;YACT,MAAM,SAAS,IAAI,CAAC,IAAI;QAC1B;IACF,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI;QAC3D;IACF;AACF;AAEO,MAAM,mBAAmB,OAAO;IACrC,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,IAAI;QAC7D,OAAO;YACL,SAAS;YACT,MAAM,SAAS,IAAI,CAAC,IAAI;QAC1B;IACF,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI;QAC3D;IACF;AACF;AAEO,MAAM,gBAAgB;IAC3B,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;QACzC,OAAO;YACL,SAAS;YACT,MAAM,SAAS,IAAI,CAAC,IAAI;QAC1B;IACF,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI;QAC3D;IACF;AACF", "debugId": null}}, {"offset": {"line": 2984, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/cartApi.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\n\r\nexport interface CartItem {\r\n  id: string;\r\n  userId: string;\r\n  userType: 'STUDENT' | 'CLASS';\r\n  itemId: string;\r\n  quantity: number;\r\n  createdAt: string;\r\n  updatedAt: string;\r\n  item: {\r\n    id: string;\r\n    name: string;\r\n    coinPrice: number;\r\n    image: string | null;\r\n    availableStock: number;\r\n  };\r\n}\r\n\r\nexport interface CartTotal {\r\n  totalCoins: number;\r\n  totalItems: number;\r\n  itemCount: number;\r\n}\r\n\r\nexport interface ApiResponse<T> {\r\n  success: boolean;\r\n  data?: T;\r\n  error?: string;\r\n  message?: string;\r\n}\r\n\r\nexport const addToCart = async (itemId: string, quantity: number = 1): Promise<ApiResponse<CartItem>> => {\r\n  try {\r\n    const response = await axiosInstance.post('/cart/add', {\r\n      itemId,\r\n      quantity\r\n    });\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || 'Failed to add item to cart'\r\n    };\r\n  }\r\n};\r\n\r\n// Get all cart items\r\nexport const getCartItems = async (): Promise<ApiResponse<CartItem[]>> => {\r\n  try {\r\n    const response = await axiosInstance.get('/cart');\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || 'Failed to fetch cart items'\r\n    };\r\n  }\r\n};\r\n\r\n// Update cart item quantity\r\nexport const updateCartItemQuantity = async (itemId: string, quantity: number): Promise<ApiResponse<CartItem>> => {\r\n  try {\r\n    const response = await axiosInstance.put(`/cart/item/${itemId}`, {\r\n      quantity\r\n    });\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || 'Failed to update cart item'\r\n    };\r\n  }\r\n};\r\n\r\n// Remove item from cart\r\nexport const removeFromCart = async (itemId: string): Promise<ApiResponse<null>> => {\r\n  try {\r\n    const response = await axiosInstance.delete(`/cart/item/${itemId}`);\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || 'Failed to remove item from cart'\r\n    };\r\n  }\r\n};\r\n\r\n// Clear entire cart\r\nexport const clearCart = async (): Promise<ApiResponse<null>> => {\r\n  try {\r\n    const response = await axiosInstance.delete('/cart/clear');\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || 'Failed to clear cart'\r\n    };\r\n  }\r\n};\r\n\r\n// Get cart total\r\nexport const getCartTotal = async (): Promise<ApiResponse<CartTotal>> => {\r\n  try {\r\n    const response = await axiosInstance.get('/cart/total');\r\n    return response.data;\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.error || 'Failed to get cart total'\r\n    };\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAgCO,MAAM,YAAY,OAAO,QAAgB,WAAmB,CAAC;IAClE,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,aAAa;YACrD;YACA;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS;QACxC;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;QACzC,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS;QACxC;IACF;AACF;AAGO,MAAM,yBAAyB,OAAO,QAAgB;IAC3D,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,QAAQ,EAAE;YAC/D;QACF;QACA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS;QACxC;IACF;AACF;AAGO,MAAM,iBAAiB,OAAO;IACnC,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,QAAQ;QAClE,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS;QACxC;IACF;AACF;AAGO,MAAM,YAAY;IACvB,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;QAC5C,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS;QACxC;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;QACzC,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,SAAS;QACxC;IACF;AACF", "debugId": null}}, {"offset": {"line": 3071, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/services/storePurchaseApi.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\nimport { PurchaseData, StoreOrder } from '@/lib/types';\r\n\r\nexport type { PurchaseData, StoreOrder };\r\n\r\nexport const purchaseItems = async (data: PurchaseData) => {\r\n  try {\r\n    const response = await axiosInstance.post('/store/purchase', data);\r\n    return {\r\n      success: true,\r\n      data: response.data.data\r\n    };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.message || error.message || 'Purchase failed'\r\n    };\r\n  }\r\n};\r\n\r\nexport const getMyOrders = async () => {\r\n  try {\r\n    const response = await axiosInstance.get('/store/orders');\r\n    return {\r\n      success: true,\r\n      data: response.data.data\r\n    };\r\n  } catch (error: any) {\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.message || error.message || 'Failed to fetch orders'\r\n    };\r\n  }\r\n};\r\n\r\nexport const getOrderDetails = async (orderId: string) => {\r\n  try {\r\n    const response = await axiosInstance.get(`/store/orders/${orderId}`);\r\n    return {\r\n      success: true,\r\n      data: response.data.data\r\n    };\r\n  } catch (error: any) {\r\n    console.error('Failed to fetch order details:', error);\r\n    return {\r\n      success: false,\r\n      error: error.response?.data?.message || error.message || 'Failed to fetch order details'\r\n    };\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;AAAA;;AAKO,MAAM,gBAAgB,OAAO;IAClC,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,mBAAmB;QAC7D,OAAO;YACL,SAAS;YACT,MAAM,SAAS,IAAI,CAAC,IAAI;QAC1B;IACF,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI;QAC3D;IACF;AACF;AAEO,MAAM,cAAc;IACzB,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;QACzC,OAAO;YACL,SAAS;YACT,MAAM,SAAS,IAAI,CAAC,IAAI;QAC1B;IACF,EAAE,OAAO,OAAY;QACnB,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI;QAC3D;IACF;AACF;AAEO,MAAM,kBAAkB,OAAO;IACpC,IAAI;QACF,MAAM,WAAW,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,SAAS;QACnE,OAAO;YACL,SAAS;YACT,MAAM,SAAS,IAAI,CAAC,IAAI;QAC1B;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;YACL,SAAS;YACT,OAAO,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI;QAC3D;IACF;AACF", "debugId": null}}, {"offset": {"line": 3127, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/dialog.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as DialogPrimitive from '@radix-ui/react-dialog';\r\nimport { XIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Dialog({ ...props }: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />;\r\n}\r\n\r\nfunction DialogTrigger({ ...props }: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />;\r\n}\r\n\r\nfunction DialogPortal({ ...props }: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />;\r\n}\r\n\r\nfunction DialogClose({ ...props }: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />;\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot=\"dialog-overlay\"\r\n      className={cn(\r\n        'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\r\n  return (\r\n    <DialogPortal data-slot=\"dialog-portal\">\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot=\"dialog-content\"\r\n        className={cn(\r\n          'bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg',\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\r\n          <XIcon />\r\n          <span className=\"sr-only\">Close</span>\r\n        </DialogPrimitive.Close>\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  );\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-header\"\r\n      className={cn('flex flex-col gap-2 text-center sm:text-left', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"dialog-footer\"\r\n      className={cn('flex flex-col-reverse gap-2 sm:flex-row sm:justify-end', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogTitle({ className, ...props }: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot=\"dialog-title\"\r\n      className={cn('text-lg leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot=\"dialog-description\"\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EAAE,GAAG,OAA0D;IAC7E,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EAAE,GAAG,OAA6D;IACvF,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EAAE,GAAG,OAA4D;IACrF,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EAAE,GAAG,OAA2D;IACnF,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0DAA0D;QACvE,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAA2D;IAC9F,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3300, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/store/%5Bid%5D/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport Image from 'next/image';\r\nimport { ArrowLeft, ShoppingCart, Coins, Package, Plus, Minus, CreditCard, Eye } from 'lucide-react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { toast } from 'sonner';\r\nimport Header from '@/app-components/Header';\r\nimport Footer from '@/app-components/Footer';\r\nimport { isAuthenticated } from '@/lib/utils';\r\nimport * as storeApi from '@/services/storeApi';\r\nimport * as cartApi from '@/services/cartApi';\r\nimport * as storePurchaseApi from '@/services/storePurchaseApi';\r\nimport { StoreItem } from '@/lib/types';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\";\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from \"@/components/ui/alert-dialog\";\r\n\r\ninterface StoreDetailPageProps {\r\n  params: Promise<{ id: string }>;\r\n}\r\n\r\nconst StoreDetailPage = ({ params }: StoreDetailPageProps) => {\r\n  const router = useRouter();\r\n  const [product, setProduct] = useState<StoreItem | null>(null);\r\n  const [latestItems, setLatestItems] = useState<StoreItem[]>([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [productId, setProductId] = useState<string>('');\r\n  const [quantity, setQuantity] = useState(1);\r\n  const [isAddingToCart, setIsAddingToCart] = useState(false);\r\n  const [isUserLoggedIn, setIsUserLoggedIn] = useState(false);\r\n  const [cart, setCart] = useState<cartApi.CartItem[]>([]);\r\n  const [showCart, setShowCart] = useState(false);\r\n  const [showDirectBuyDialog, setShowDirectBuyDialog] = useState(false);\r\n  const [isDirectBuying, setIsDirectBuying] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const getParamsId = async () => {\r\n      const { id } = await params;\r\n      setProductId(id);\r\n    };\r\n\r\n    getParamsId();\r\n  }, [params]);\r\n\r\n  useEffect(() => {\r\n    const authStatus = isAuthenticated();\r\n    setIsUserLoggedIn(authStatus.isAuth);\r\n\r\n    if (authStatus.isAuth) {\r\n      loadCartItems();\r\n    }\r\n  }, []);\r\n\r\n  const loadCartItems = async () => {\r\n    try {\r\n      const result = await cartApi.getCartItems();\r\n      if (result.success && result.data) {\r\n        setCart(result.data);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading cart:', error);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const fetchProductAndLatestItems = async () => {\r\n      try {\r\n        if (!productId) return;\r\n\r\n        setIsLoading(true);\r\n\r\n        // Fetch current product\r\n        const productResult = await storeApi.getStoreItemById(productId);\r\n\r\n        if (!productResult.success) {\r\n          toast.error(productResult.error || 'Failed to load product');\r\n          router.push('/store');\r\n          return;\r\n        }\r\n\r\n        setProduct(productResult.data);\r\n\r\n        const latestItemsResult = await storeApi.getAllStoreItems();\r\n\r\n        if (latestItemsResult.success && latestItemsResult.data) {\r\n          const latest4Items = latestItemsResult.data\r\n            .filter((item: StoreItem) => item.id !== productId)\r\n            .slice(0, 4);\r\n          setLatestItems(latest4Items);\r\n        }\r\n      } catch (error) {\r\n        console.error('Error fetching data:', error);\r\n        toast.error('Failed to load product');\r\n        router.push('/store');\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchProductAndLatestItems();\r\n  }, [productId, router]);\r\n\r\n  const handleQuantityChange = (newQuantity: number) => {\r\n    if (newQuantity < 1) return;\r\n    if (product && newQuantity > product.availableStock) {\r\n      toast.error(`Only ${product.availableStock} items available`);\r\n      return;\r\n    }\r\n    setQuantity(newQuantity);\r\n  };\r\n\r\n  const addToCart = async () => {\r\n    if (!isUserLoggedIn) {\r\n      toast.error(\"Please login to add items to cart\");\r\n      return;\r\n    }\r\n\r\n    if (!product) return;\r\n\r\n    if (product.availableStock === 0) {\r\n      toast.error(\"Item is out of stock\");\r\n      return;\r\n    }\r\n\r\n    if (quantity > product.availableStock) {\r\n      toast.error(`Only ${product.availableStock} items available`);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setIsAddingToCart(true);\r\n      const result = await cartApi.addToCart(product.id, quantity);\r\n\r\n      if (result.success) {\r\n        toast.success(`${quantity} item(s) added to cart!`);\r\n        await loadCartItems(); // Reload cart items\r\n        setQuantity(1); // Reset quantity after adding to cart\r\n      } else {\r\n        toast.error(result.error || \"Failed to add item to cart\");\r\n      }\r\n    } catch (error) {\r\n      console.error('Error adding to cart:', error);\r\n      toast.error(\"Failed to add item to cart\");\r\n    } finally {\r\n      setIsAddingToCart(false);\r\n    }\r\n  };\r\n\r\n  const handleDirectBuy = () => {\r\n    if (!isUserLoggedIn) {\r\n      toast.error(\"Please login to purchase items\");\r\n      return;\r\n    }\r\n\r\n    if (!product) return;\r\n\r\n    if (product.availableStock === 0) {\r\n      toast.error(\"Item is out of stock\");\r\n      return;\r\n    }\r\n\r\n    if (quantity > product.availableStock) {\r\n      toast.error(`Only ${product.availableStock} items available`);\r\n      return;\r\n    }\r\n\r\n    setShowDirectBuyDialog(true);\r\n  };\r\n\r\n  const confirmDirectPurchase = async () => {\r\n    if (!product) return;\r\n\r\n    try {\r\n      setIsDirectBuying(true);\r\n      const totalCoins = product.coinPrice * quantity;\r\n\r\n      const purchaseData: storePurchaseApi.PurchaseData = {\r\n        cartItems: [{\r\n          id: product.id,\r\n          name: product.name,\r\n          coinPrice: product.coinPrice,\r\n          quantity: quantity,\r\n          image: product.image || ''\r\n        }],\r\n        totalCoins\r\n      };\r\n\r\n      const result = await storePurchaseApi.purchaseItems(purchaseData);\r\n\r\n      if (!result.success) {\r\n        if (result.error === 'PROFILE_NOT_APPROVED') {\r\n          const errorMessage = result.data?.message || 'Your profile is not approved yet. Please complete your profile and wait for admin approval.';\r\n          toast.error(errorMessage);\r\n          setShowDirectBuyDialog(false);\r\n          return;\r\n        }\r\n        throw new Error(result.error);\r\n      }\r\n\r\n      const orderId = result.data?.orderId || result.data?.firstOrderId || 'Unknown';\r\n      toast.success(`Order placed successfully! Order ID: ${orderId.slice(-8)}. Coins deducted. Your order is pending admin approval.`);\r\n\r\n      setShowDirectBuyDialog(false);\r\n      setQuantity(1);\r\n\r\n      // Refresh both product and latest items\r\n      const productResult = await storeApi.getStoreItemById(productId);\r\n      if (productResult.success) {\r\n        setProduct(productResult.data);\r\n      }\r\n\r\n      const latestItemsResult = await storeApi.getAllStoreItems();\r\n      if (latestItemsResult.success && latestItemsResult.data) {\r\n        const latest4Items = latestItemsResult.data\r\n          .filter((item: StoreItem) => item.id !== productId)\r\n          .slice(0, 4);\r\n        setLatestItems(latest4Items);\r\n      }\r\n    } catch (error: any) {\r\n      console.error('Error details:', error);\r\n      toast.error(error.message || 'Purchase failed');\r\n      setShowDirectBuyDialog(false);\r\n    } finally {\r\n      setIsDirectBuying(false);\r\n    }\r\n  };\r\n\r\n  const removeFromCart = async (productId: string) => {\r\n    try {\r\n      const result = await cartApi.removeFromCart(productId);\r\n      if (result.success) {\r\n        await loadCartItems();\r\n        toast.success(\"Item removed from cart!\");\r\n      } else {\r\n        toast.error(result.error || \"Failed to remove item from cart\");\r\n      }\r\n    } catch (error) {\r\n      console.error('Error removing from cart:', error);\r\n      toast.error(\"Failed to remove item from cart\");\r\n    }\r\n  };\r\n\r\n  const updateCartQuantity = async (productId: string, newQuantity: number) => {\r\n    try {\r\n      const cartItem = cart.find(item => item.itemId === productId);\r\n\r\n      if (cartItem && newQuantity > cartItem.item.availableStock) {\r\n        toast.error(`Only ${cartItem.item.availableStock} items available in stock`);\r\n        return;\r\n      }\r\n\r\n      const result = await cartApi.updateCartItemQuantity(productId, newQuantity);\r\n      if (result.success) {\r\n        await loadCartItems();\r\n        if (newQuantity === 0) {\r\n          toast.success(\"Item removed from cart!\");\r\n        }\r\n      } else {\r\n        // Handle specific error messages from backend\r\n        const errorMessage = result.error || \"Failed to update cart item\";\r\n        if (errorMessage.includes(\"stock\") || errorMessage.includes(\"available\")) {\r\n          toast.error(\"Item is out of stock or insufficient quantity available\");\r\n        } else {\r\n          toast.error(errorMessage);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Error updating cart:', error);\r\n      toast.error(\"Failed to update cart item\");\r\n    }\r\n  };\r\n\r\n  const getTotalCartPrice = () => {\r\n    return cart.reduce((total, item) => {\r\n      return total + (item.item.coinPrice * item.quantity);\r\n    }, 0);\r\n  };\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <>\r\n        <Header />\r\n        <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\r\n          <div className=\"container mx-auto px-4 py-8\">\r\n            <Skeleton className=\"h-10 w-32 mb-6\" />\r\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\r\n              <Skeleton className=\"h-96 w-full rounded-lg\" />\r\n              <div className=\"space-y-4\">\r\n                <Skeleton className=\"h-8 w-3/4\" />\r\n                <Skeleton className=\"h-4 w-full\" />\r\n                <Skeleton className=\"h-4 w-2/3\" />\r\n                <Skeleton className=\"h-6 w-1/4\" />\r\n                <Skeleton className=\"h-10 w-full\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <Footer />\r\n      </>\r\n    );\r\n  }\r\n\r\n  if (!product) {\r\n    return (\r\n      <>\r\n        <Header />\r\n        <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center\">\r\n          <div className=\"text-center\">\r\n            <h1 className=\"text-2xl font-bold text-foreground mb-4\">Product Not Found</h1>\r\n            <Button onClick={() => router.push('/store')} className=\"bg-customOrange hover:bg-orange-600\">\r\n              <ArrowLeft className=\"w-4 h-4 mr-2\" />\r\n              Back to Store\r\n            </Button>\r\n          </div>\r\n        </div>\r\n        <Footer />\r\n      </>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <Header />\r\n      <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\r\n        <div className=\"container mx-auto px-4 py-8\">\r\n          {/* Back Button */}\r\n          <Button\r\n            variant=\"ghost\"\r\n            onClick={() => router.push('/store')}\r\n            className=\"mb-6 hover:bg-muted\"\r\n          >\r\n            <ArrowLeft className=\"w-4 h-4 mr-2\" />\r\n            Back to Store\r\n          </Button>\r\n\r\n          {/* Product Details */}\r\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12\">\r\n            {/* Product Image */}\r\n            <Card className=\"overflow-hidden\">\r\n              <CardContent className=\"p-0\">\r\n                <div className=\"relative h-96 lg:h-[500px] bg-muted/30 flex items-center justify-center\">\r\n                  <Image\r\n                    src={\r\n                      product.image?.startsWith('http')\r\n                        ? product.image\r\n                        : `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${product.image?.startsWith('/') ? product.image.substring(1) : product.image || 'uploads/store/placeholder.jpg'}`\r\n                    }\r\n                    alt={product.name}\r\n                    className=\"object-contain w-full h-full\"\r\n                    width={500}\r\n                    height={500}\r\n                    onError={(e) => {\r\n                      const target = e.target as HTMLImageElement;\r\n                      target.src = \"/logo.png\";\r\n                    }}\r\n                  />\r\n                  {product.availableStock === 0 && (\r\n                    <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\">\r\n                      <Badge variant=\"destructive\" className=\"text-lg px-4 py-2\">Out of Stock</Badge>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n\r\n            {/* Product Information */}\r\n            <div className=\"space-y-6\">\r\n              <div>\r\n                <div className=\"flex items-start justify-between mb-2\">\r\n                  <h1 className=\"text-3xl font-bold text-foreground\">{product.name}</h1>\r\n                  <Badge variant=\"secondary\" className=\"text-sm\">\r\n                    {product.category}\r\n                  </Badge>\r\n                </div>\r\n                <p className=\"text-muted-foreground text-lg leading-relaxed\">\r\n                  {product.description}\r\n                </p>\r\n              </div>\r\n\r\n              {/* Price */}\r\n              <div className=\"bg-card p-6 rounded-lg border\">\r\n                <div className=\"flex items-center gap-2 mb-2\">\r\n                  <Coins className=\"w-6 h-6 text-customOrange\" />\r\n                  <span className=\"text-3xl font-bold text-customOrange\">\r\n                    {product.coinPrice} coins\r\n                  </span>\r\n                </div>\r\n                <p className=\"text-sm text-muted-foreground\">\r\n                  Pay with your UEST coins\r\n                </p>\r\n              </div>\r\n\r\n              {/* Stock Information */}\r\n              <div className=\"bg-card p-4 rounded-lg border\">\r\n                <div className=\"flex items-center gap-2 mb-2\">\r\n                  <Package className=\"w-5 h-5 text-muted-foreground\" />\r\n                  <span className=\"font-medium text-card-foreground\">Stock Information</span>\r\n                </div>\r\n                <div className=\"space-y-1 text-sm\">\r\n                  <div className=\"flex justify-between\">\r\n                    <span className=\"text-muted-foreground\">Available:</span>\r\n                    <span className={`font-medium ${product.availableStock === 0 ? 'text-red-500' : 'text-green-600'}`}>\r\n                      {product.availableStock} units\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n\r\n              {/* Quantity Selector and Add to Cart */}\r\n              {product.availableStock > 0 && (\r\n                <div className=\"space-y-4\">\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-card-foreground mb-2\">\r\n                      Quantity\r\n                    </label>\r\n                    <div className=\"flex items-center gap-3\">\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        size=\"sm\"\r\n                        onClick={() => handleQuantityChange(quantity - 1)}\r\n                        disabled={quantity <= 1}\r\n                      >\r\n                        <Minus className=\"w-4 h-4\" />\r\n                      </Button>\r\n                      <span className=\"w-12 text-center font-medium text-lg\">{quantity}</span>\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        size=\"sm\"\r\n                        onClick={() => handleQuantityChange(quantity + 1)}\r\n                        disabled={quantity >= product.availableStock}\r\n                      >\r\n                        <Plus className=\"w-4 h-4\" />\r\n                      </Button>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"space-y-4\">\r\n                    <div className=\"flex items-center justify-between p-3 bg-muted/50 rounded-lg\">\r\n                      <span className=\"text-sm text-muted-foreground\">Total Cost:</span>\r\n                      <span className=\"font-bold text-lg text-customOrange flex items-center\">\r\n                        <Coins className=\"w-5 h-5 mr-1\" />\r\n                        {product.coinPrice * quantity} coins\r\n                      </span>\r\n                    </div>\r\n\r\n                    <div className=\"grid grid-cols-1 gap-3\">\r\n                      <Button\r\n                        onClick={addToCart}\r\n                        disabled={isAddingToCart || !isUserLoggedIn}\r\n                        variant=\"outline\"\r\n                        className=\"w-full border-customOrange text-customOrange hover:bg-customOrange hover:text-white py-3 text-lg\"\r\n                      >\r\n                        {isAddingToCart ? (\r\n                          <>\r\n                            <div className=\"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2\" />\r\n                            Adding to Cart...\r\n                          </>\r\n                        ) : (\r\n                          <>\r\n                            <ShoppingCart className=\"w-5 h-5 mr-2\" />\r\n                            Add to Cart\r\n                          </>\r\n                        )}\r\n                      </Button>\r\n\r\n                      <Button\r\n                        onClick={handleDirectBuy}\r\n                        disabled={!isUserLoggedIn}\r\n                        className=\"w-full bg-customOrange hover:bg-orange-600 text-white py-3 text-lg\"\r\n                      >\r\n                        <CreditCard className=\"w-5 h-5 mr-2\" />\r\n                        Buy Now\r\n                      </Button>\r\n                    </div>\r\n\r\n                    {cart.length > 0 && (\r\n                      <Button\r\n                        onClick={() => setShowCart(true)}\r\n                        variant=\"secondary\"\r\n                        className=\"w-full\"\r\n                      >\r\n                        <Eye className=\"w-4 h-4 mr-2\" />\r\n                        View Cart ({cart.reduce((total, item) => total + item.quantity, 0)} items)\r\n                      </Button>\r\n                    )}\r\n\r\n                    {!isUserLoggedIn && (\r\n                      <p className=\"text-sm text-muted-foreground text-center\">\r\n                        Please login to purchase items\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              )}\r\n\r\n              {product.availableStock === 0 && (\r\n                <div className=\"bg-destructive/10 border border-destructive/20 p-4 rounded-lg\">\r\n                  <p className=\"text-destructive font-medium text-center\">\r\n                    This item is currently out of stock\r\n                  </p>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Additional Information */}\r\n          <Card className='mb-8'>\r\n            <CardContent className=\"p-6\">\r\n              <h2 className=\"text-xl font-semibold text-card-foreground mb-4\">Product Information</h2>\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                <div>\r\n                  <h3 className=\"font-medium text-card-foreground mb-2\">Category</h3>\r\n                  <p className=\"text-muted-foreground\">{product.category}</p>\r\n                </div>\r\n                <div>\r\n                  <h3 className=\"font-medium text-card-foreground mb-2\">Status</h3>\r\n                  <Badge variant={product.status === 'ACTIVE' ? 'default' : 'secondary'}>\r\n                    {product.status}\r\n                  </Badge>\r\n                </div>\r\n                <div>\r\n                  <h3 className=\"font-medium text-card-foreground mb-2\">Added On</h3>\r\n                  <p className=\"text-muted-foreground\">\r\n                    {new Date(product.createdAt).toLocaleDateString('en-US', {\r\n                      year: 'numeric',\r\n                      month: 'long',\r\n                      day: 'numeric'\r\n                    })}\r\n                  </p>\r\n                </div>\r\n                <div>\r\n                  <h3 className=\"font-medium text-card-foreground mb-2\">Last Updated</h3>\r\n                  <p className=\"text-muted-foreground\">\r\n                    {new Date(product.updatedAt).toLocaleDateString('en-US', {\r\n                      year: 'numeric',\r\n                      month: 'long',\r\n                      day: 'numeric'\r\n                    })}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Latest Items Section */}\r\n          {latestItems.length > 0 && (\r\n            <Card className=\"mb-8\">\r\n              <CardContent className=\"p-6\">\r\n                <h2 className=\"text-xl font-semibold text-card-foreground mb-6\">Latest Items</h2>\r\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\">\r\n                  {latestItems.map((item) => (\r\n                    <Card\r\n                      key={item.id}\r\n                      className=\"overflow-hidden group bg-card border shadow-sm hover:shadow-lg transition-all duration-300 cursor-pointer\"\r\n                      onClick={() => router.push(`/store/${item.id}`)}\r\n                    >\r\n                      <div className=\"relative h-48 bg-muted/30 flex items-center justify-center\">\r\n                        <Image\r\n                          src={\r\n                            item.image?.startsWith('http')\r\n                              ? item.image\r\n                              : `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${item.image?.startsWith('/') ? item.image.substring(1) : item.image || 'uploads/store/placeholder.jpg'}`\r\n                          }\r\n                          alt={item.name}\r\n                          className=\"object-contain w-full h-full\"\r\n                          width={200}\r\n                          height={200}\r\n                          onError={(e) => {\r\n                            const target = e.target as HTMLImageElement;\r\n                            target.src = \"/logo.png\";\r\n                          }}\r\n                        />\r\n                        {item.availableStock === 0 && (\r\n                          <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\">\r\n                            <Badge variant=\"destructive\" className=\"text-sm\">Out of Stock</Badge>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                      <CardContent className=\"p-4\">\r\n                        <div className=\"space-y-2\">\r\n                          <div className=\"flex items-start justify-between\">\r\n                            <h3 className=\"font-semibold text-card-foreground text-sm line-clamp-2 group-hover:text-customOrange transition-colors\">\r\n                              {item.name}\r\n                            </h3>\r\n                            <Badge variant=\"secondary\" className=\"text-xs ml-2 shrink-0\">\r\n                              {item.category}\r\n                            </Badge>\r\n                          </div>\r\n                          <p className=\"text-muted-foreground text-xs line-clamp-2\">\r\n                            {item.description}\r\n                          </p>\r\n                          <div className=\"flex items-center justify-between pt-2\">\r\n                            <div className=\"flex items-center gap-1\">\r\n                              <Coins className=\"w-4 h-4 text-customOrange\" />\r\n                              <span className=\"font-bold text-customOrange text-sm\">\r\n                                {item.coinPrice}\r\n                              </span>\r\n                            </div>\r\n                            <span className={`text-xs ${item.availableStock === 0 ? 'text-red-500' : 'text-green-600'}`}>\r\n                              {item.availableStock} left\r\n                            </span>\r\n                          </div>\r\n                        </div>\r\n                      </CardContent>\r\n                    </Card>\r\n                  ))}\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          )}\r\n        </div>\r\n      </div>\r\n      <Footer />\r\n\r\n      {/* Shopping Cart Dialog */}\r\n      <Dialog open={showCart} onOpenChange={setShowCart}>\r\n        <DialogContent className=\"max-w-2xl max-h-[80vh] overflow-y-auto\">\r\n          <DialogHeader>\r\n            <DialogTitle className=\"flex items-center gap-2\">\r\n              <ShoppingCart className=\"w-5 h-5\" />\r\n              Shopping Cart ({cart.reduce((total, item) => total + item.quantity, 0)} items)\r\n            </DialogTitle>\r\n            <DialogDescription>\r\n              Review your items before checkout\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n\r\n          <div className=\"space-y-4\">\r\n            {cart.length === 0 ? (\r\n              <div className=\"text-center py-8\">\r\n                <ShoppingCart className=\"w-12 h-12 mx-auto text-muted-foreground mb-4\" />\r\n                <p className=\"text-muted-foreground\">Your cart is empty</p>\r\n              </div>\r\n            ) : (\r\n              <>\r\n                {cart.map((item) => (\r\n                  <div key={item.id} className=\"flex items-center gap-4 p-4 border rounded-lg bg-card\">\r\n                    <Image\r\n                      src={\r\n                        item.item.image?.startsWith('http')\r\n                          ? item.item.image\r\n                          : `${process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4005/'}${item.item.image?.startsWith('/') ? item.item.image.substring(1) : item.item.image || 'uploads/store/placeholder.jpg'}`\r\n                      }\r\n                      alt={item.item.name}\r\n                      width={60}\r\n                      height={60}\r\n                      className=\"rounded object-cover\"\r\n                      onError={(e) => {\r\n                        const target = e.target as HTMLImageElement;\r\n                        target.src = \"/logo.png\";\r\n                      }}\r\n                    />\r\n                    <div className=\"flex-1\">\r\n                      <h4 className=\"font-medium text-card-foreground\">{item.item.name}</h4>\r\n                      <p className=\"text-customOrange font-semibold flex items-center\">\r\n                        <Coins className=\"w-4 h-4 mr-1\" />\r\n                        {item.item.coinPrice} coins\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <Button\r\n                        size=\"sm\"\r\n                        variant=\"outline\"\r\n                        onClick={() => updateCartQuantity(item.itemId, item.quantity - 1)}\r\n                      >\r\n                        <Minus className=\"w-3 h-3\" />\r\n                      </Button>\r\n                      <span className=\"w-8 text-center\">{item.quantity}</span>\r\n                      <Button\r\n                        size=\"sm\"\r\n                        variant=\"outline\"\r\n                        onClick={() => updateCartQuantity(item.itemId, item.quantity + 1)}\r\n                      >\r\n                        <Plus className=\"w-3 h-3\" />\r\n                      </Button>\r\n                    </div>\r\n                    <Button\r\n                      size=\"sm\"\r\n                      variant=\"destructive\"\r\n                      onClick={() => removeFromCart(item.itemId)}\r\n                    >\r\n                      Remove\r\n                    </Button>\r\n                  </div>\r\n                ))}\r\n\r\n                <div className=\"border-t pt-4\">\r\n                  <div className=\"flex justify-between items-center text-lg font-semibold\">\r\n                    <span>Total:</span>\r\n                    <span className=\"text-customOrange flex items-center\">\r\n                      <Coins className=\"w-5 h-5 mr-1\" />\r\n                      {getTotalCartPrice()} coins\r\n                    </span>\r\n                  </div>\r\n                </div>\r\n              </>\r\n            )}\r\n          </div>\r\n\r\n          <DialogFooter>\r\n            <Button variant=\"outline\" onClick={() => setShowCart(false)}>\r\n              Continue Shopping\r\n            </Button>\r\n            {cart.length > 0 && (\r\n              <Button\r\n                onClick={() => {\r\n                  setShowCart(false);\r\n                  router.push('/store');\r\n                }}\r\n                className=\"bg-customOrange hover:bg-orange-600\"\r\n              >\r\n                <ShoppingCart className=\"w-4 h-4 mr-2\" />\r\n                Go to Store & Checkout\r\n              </Button>\r\n            )}\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* Direct Buy Confirmation Dialog */}\r\n      <AlertDialog open={showDirectBuyDialog} onOpenChange={setShowDirectBuyDialog}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Confirm Direct Purchase</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Are you sure you want to purchase this item directly for <strong>{product?.coinPrice * quantity} coins</strong>?\r\n              <br />\r\n              <br />\r\n              <div className=\"space-y-2\">\r\n                <div className=\"flex justify-between text-sm\">\r\n                  <span>{product?.name} x{quantity}</span>\r\n                  <span>{product?.coinPrice * quantity} coins</span>\r\n                </div>\r\n              </div>\r\n              <br />\r\n              This action cannot be undone and coins will be deducted from your account immediately.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel>Cancel</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={confirmDirectPurchase}\r\n              disabled={isDirectBuying}\r\n              className=\"bg-customOrange hover:bg-customOrange/90\"\r\n            >\r\n              {isDirectBuying ? (\r\n                <>\r\n                  <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2\" />\r\n                  Processing...\r\n                </>\r\n              ) : (\r\n                'Confirm Purchase'\r\n              )}\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default StoreDetailPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAQA;AA1BA;;;;;;;;;;;;;;;;;;;AAyCA,MAAM,kBAAkB,CAAC,EAAE,MAAM,EAAwB;IACvD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC9D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;YACrB,aAAa;QACf;QAEA;IACF,GAAG;QAAC;KAAO;IAEX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,CAAA,GAAA,mHAAA,CAAA,kBAAe,AAAD;QACjC,kBAAkB,WAAW,MAAM;QAEnC,IAAI,WAAW,MAAM,EAAE;YACrB;QACF;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,0HAAA,CAAA,eAAoB,AAAD;YACxC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gBACjC,QAAQ,OAAO,IAAI;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;QACvC;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,6BAA6B;YACjC,IAAI;gBACF,IAAI,CAAC,WAAW;gBAEhB,aAAa;gBAEb,wBAAwB;gBACxB,MAAM,gBAAgB,MAAM,CAAA,GAAA,2HAAA,CAAA,mBAAyB,AAAD,EAAE;gBAEtD,IAAI,CAAC,cAAc,OAAO,EAAE;oBAC1B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,cAAc,KAAK,IAAI;oBACnC,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,WAAW,cAAc,IAAI;gBAE7B,MAAM,oBAAoB,MAAM,CAAA,GAAA,2HAAA,CAAA,mBAAyB,AAAD;gBAExD,IAAI,kBAAkB,OAAO,IAAI,kBAAkB,IAAI,EAAE;oBACvD,MAAM,eAAe,kBAAkB,IAAI,CACxC,MAAM,CAAC,CAAC,OAAoB,KAAK,EAAE,KAAK,WACxC,KAAK,CAAC,GAAG;oBACZ,eAAe;gBACjB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO,IAAI,CAAC;YACd,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG;QAAC;QAAW;KAAO;IAEtB,MAAM,uBAAuB,CAAC;QAC5B,IAAI,cAAc,GAAG;QACrB,IAAI,WAAW,cAAc,QAAQ,cAAc,EAAE;YACnD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,QAAQ,cAAc,CAAC,gBAAgB,CAAC;YAC5D;QACF;QACA,YAAY;IACd;IAEA,MAAM,YAAY;QAChB,IAAI,CAAC,gBAAgB;YACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,SAAS;QAEd,IAAI,QAAQ,cAAc,KAAK,GAAG;YAChC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,WAAW,QAAQ,cAAc,EAAE;YACrC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,QAAQ,cAAc,CAAC,gBAAgB,CAAC;YAC5D;QACF;QAEA,IAAI;YACF,kBAAkB;YAClB,MAAM,SAAS,MAAM,CAAA,GAAA,0HAAA,CAAA,YAAiB,AAAD,EAAE,QAAQ,EAAE,EAAE;YAEnD,IAAI,OAAO,OAAO,EAAE;gBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,SAAS,uBAAuB,CAAC;gBAClD,MAAM,iBAAiB,oBAAoB;gBAC3C,YAAY,IAAI,sCAAsC;YACxD,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,gBAAgB;YACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,SAAS;QAEd,IAAI,QAAQ,cAAc,KAAK,GAAG;YAChC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,WAAW,QAAQ,cAAc,EAAE;YACrC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,QAAQ,cAAc,CAAC,gBAAgB,CAAC;YAC5D;QACF;QAEA,uBAAuB;IACzB;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,SAAS;QAEd,IAAI;YACF,kBAAkB;YAClB,MAAM,aAAa,QAAQ,SAAS,GAAG;YAEvC,MAAM,eAA8C;gBAClD,WAAW;oBAAC;wBACV,IAAI,QAAQ,EAAE;wBACd,MAAM,QAAQ,IAAI;wBAClB,WAAW,QAAQ,SAAS;wBAC5B,UAAU;wBACV,OAAO,QAAQ,KAAK,IAAI;oBAC1B;iBAAE;gBACF;YACF;YAEA,MAAM,SAAS,MAAM,CAAA,GAAA,mIAAA,CAAA,gBAA8B,AAAD,EAAE;YAEpD,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,IAAI,OAAO,KAAK,KAAK,wBAAwB;oBAC3C,MAAM,eAAe,OAAO,IAAI,EAAE,WAAW;oBAC7C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACZ,uBAAuB;oBACvB;gBACF;gBACA,MAAM,IAAI,MAAM,OAAO,KAAK;YAC9B;YAEA,MAAM,UAAU,OAAO,IAAI,EAAE,WAAW,OAAO,IAAI,EAAE,gBAAgB;YACrE,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,qCAAqC,EAAE,QAAQ,KAAK,CAAC,CAAC,GAAG,uDAAuD,CAAC;YAEhI,uBAAuB;YACvB,YAAY;YAEZ,wCAAwC;YACxC,MAAM,gBAAgB,MAAM,CAAA,GAAA,2HAAA,CAAA,mBAAyB,AAAD,EAAE;YACtD,IAAI,cAAc,OAAO,EAAE;gBACzB,WAAW,cAAc,IAAI;YAC/B;YAEA,MAAM,oBAAoB,MAAM,CAAA,GAAA,2HAAA,CAAA,mBAAyB,AAAD;YACxD,IAAI,kBAAkB,OAAO,IAAI,kBAAkB,IAAI,EAAE;gBACvD,MAAM,eAAe,kBAAkB,IAAI,CACxC,MAAM,CAAC,CAAC,OAAoB,KAAK,EAAE,KAAK,WACxC,KAAK,CAAC,GAAG;gBACZ,eAAe;YACjB;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,kBAAkB;YAChC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC7B,uBAAuB;QACzB,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,0HAAA,CAAA,iBAAsB,AAAD,EAAE;YAC5C,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM;gBACN,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,qBAAqB,OAAO,WAAmB;QACnD,IAAI;YACF,MAAM,WAAW,KAAK,IAAI,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;YAEnD,IAAI,YAAY,cAAc,SAAS,IAAI,CAAC,cAAc,EAAE;gBAC1D,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,SAAS,IAAI,CAAC,cAAc,CAAC,yBAAyB,CAAC;gBAC3E;YACF;YAEA,MAAM,SAAS,MAAM,CAAA,GAAA,0HAAA,CAAA,yBAA8B,AAAD,EAAE,WAAW;YAC/D,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM;gBACN,IAAI,gBAAgB,GAAG;oBACrB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB;YACF,OAAO;gBACL,8CAA8C;gBAC9C,MAAM,eAAe,OAAO,KAAK,IAAI;gBACrC,IAAI,aAAa,QAAQ,CAAC,YAAY,aAAa,QAAQ,CAAC,cAAc;oBACxE,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd,OAAO;oBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,oBAAoB;QACxB,OAAO,KAAK,MAAM,CAAC,CAAC,OAAO;YACzB,OAAO,QAAS,KAAK,IAAI,CAAC,SAAS,GAAG,KAAK,QAAQ;QACrD,GAAG;IACL;IAEA,IAAI,WAAW;QACb,qBACE;;8BACE,8OAAC,mIAAA,CAAA,UAAM;;;;;8BACP,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oIAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC,oIAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAK5B,8OAAC,mIAAA,CAAA,UAAM;;;;;;;IAGb;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE;;8BACE,8OAAC,mIAAA,CAAA,UAAM;;;;;8BACP,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0C;;;;;;0CACxD,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS,IAAM,OAAO,IAAI,CAAC;gCAAW,WAAU;;kDACtD,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;8BAK5C,8OAAC,mIAAA,CAAA,UAAM;;;;;;;IAGb;IAEA,qBACE;;0BACE,8OAAC,mIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,OAAO,IAAI,CAAC;4BAC3B,WAAU;;8CAEV,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAKxC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KACE,QAAQ,KAAK,EAAE,WAAW,UACtB,QAAQ,KAAK,GACb,GAAG,8DAAwC,2BAA2B,QAAQ,KAAK,EAAE,WAAW,OAAO,QAAQ,KAAK,CAAC,SAAS,CAAC,KAAK,QAAQ,KAAK,IAAI,iCAAiC;oDAE5L,KAAK,QAAQ,IAAI;oDACjB,WAAU;oDACV,OAAO;oDACP,QAAQ;oDACR,SAAS,CAAC;wDACR,MAAM,SAAS,EAAE,MAAM;wDACvB,OAAO,GAAG,GAAG;oDACf;;;;;;gDAED,QAAQ,cAAc,KAAK,mBAC1B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAc,WAAU;kEAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQrE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAsC,QAAQ,IAAI;;;;;;sEAChE,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;sEAClC,QAAQ,QAAQ;;;;;;;;;;;;8DAGrB,8OAAC;oDAAE,WAAU;8DACV,QAAQ,WAAW;;;;;;;;;;;;sDAKxB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;4DAAK,WAAU;;gEACb,QAAQ,SAAS;gEAAC;;;;;;;;;;;;;8DAGvB,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;sDAM/C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,wMAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,8OAAC;4DAAK,WAAU;sEAAmC;;;;;;;;;;;;8DAErD,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,8OAAC;gEAAK,WAAW,CAAC,YAAY,EAAE,QAAQ,cAAc,KAAK,IAAI,iBAAiB,kBAAkB;;oEAC/F,QAAQ,cAAc;oEAAC;;;;;;;;;;;;;;;;;;;;;;;;wCAO/B,QAAQ,cAAc,GAAG,mBACxB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAAsD;;;;;;sEAGvE,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,qBAAqB,WAAW;oEAC/C,UAAU,YAAY;8EAEtB,cAAA,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;8EAEnB,8OAAC;oEAAK,WAAU;8EAAwC;;;;;;8EACxD,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,qBAAqB,WAAW;oEAC/C,UAAU,YAAY,QAAQ,cAAc;8EAE5C,cAAA,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8DAKtB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAgC;;;;;;8EAChD,8OAAC;oEAAK,WAAU;;sFACd,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAChB,QAAQ,SAAS,GAAG;wEAAS;;;;;;;;;;;;;sEAIlC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAS;oEACT,UAAU,kBAAkB,CAAC;oEAC7B,SAAQ;oEACR,WAAU;8EAET,+BACC;;0FACE,8OAAC;gFAAI,WAAU;;;;;;4EAAwF;;qGAIzG;;0FACE,8OAAC,sNAAA,CAAA,eAAY;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;8EAM/C,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAS;oEACT,UAAU,CAAC;oEACX,WAAU;;sFAEV,8OAAC,kNAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;wDAK1C,KAAK,MAAM,GAAG,mBACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS,IAAM,YAAY;4DAC3B,SAAQ;4DACR,WAAU;;8EAEV,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEAAiB;gEACpB,KAAK,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,QAAQ,EAAE;gEAAG;;;;;;;wDAItE,CAAC,gCACA,8OAAC;4DAAE,WAAU;sEAA4C;;;;;;;;;;;;;;;;;;wCAQhE,QAAQ,cAAc,KAAK,mBAC1B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DAA2C;;;;;;;;;;;;;;;;;;;;;;;sCAShE,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAG,WAAU;kDAAkD;;;;;;kDAChE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAE,WAAU;kEAAyB,QAAQ,QAAQ;;;;;;;;;;;;0DAExD,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAS,QAAQ,MAAM,KAAK,WAAW,YAAY;kEACvD,QAAQ,MAAM;;;;;;;;;;;;0DAGnB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAE,WAAU;kEACV,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB,CAAC,SAAS;4DACvD,MAAM;4DACN,OAAO;4DACP,KAAK;wDACP;;;;;;;;;;;;0DAGJ,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAE,WAAU;kEACV,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB,CAAC,SAAS;4DACvD,MAAM;4DACN,OAAO;4DACP,KAAK;wDACP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAQT,YAAY,MAAM,GAAG,mBACpB,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAG,WAAU;kDAAkD;;;;;;kDAChE,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC,gIAAA,CAAA,OAAI;gDAEH,WAAU;gDACV,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;;kEAE9C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,6HAAA,CAAA,UAAK;gEACJ,KACE,KAAK,KAAK,EAAE,WAAW,UACnB,KAAK,KAAK,GACV,GAAG,8DAAwC,2BAA2B,KAAK,KAAK,EAAE,WAAW,OAAO,KAAK,KAAK,CAAC,SAAS,CAAC,KAAK,KAAK,KAAK,IAAI,iCAAiC;gEAEnL,KAAK,KAAK,IAAI;gEACd,WAAU;gEACV,OAAO;gEACP,QAAQ;gEACR,SAAS,CAAC;oEACR,MAAM,SAAS,EAAE,MAAM;oEACvB,OAAO,GAAG,GAAG;gEACf;;;;;;4DAED,KAAK,cAAc,KAAK,mBACvB,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAc,WAAU;8EAAU;;;;;;;;;;;;;;;;;kEAIvD,8OAAC,gIAAA,CAAA,cAAW;wDAAC,WAAU;kEACrB,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFACX,KAAK,IAAI;;;;;;sFAEZ,8OAAC,iIAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAY,WAAU;sFAClC,KAAK,QAAQ;;;;;;;;;;;;8EAGlB,8OAAC;oEAAE,WAAU;8EACV,KAAK,WAAW;;;;;;8EAEnB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,oMAAA,CAAA,QAAK;oFAAC,WAAU;;;;;;8FACjB,8OAAC;oFAAK,WAAU;8FACb,KAAK,SAAS;;;;;;;;;;;;sFAGnB,8OAAC;4EAAK,WAAW,CAAC,QAAQ,EAAE,KAAK,cAAc,KAAK,IAAI,iBAAiB,kBAAkB;;gFACxF,KAAK,cAAc;gFAAC;;;;;;;;;;;;;;;;;;;;;;;;;+CA/CxB,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BA4D5B,8OAAC,mIAAA,CAAA,UAAM;;;;;0BAGP,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAU,cAAc;0BACpC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAAY;wCACpB,KAAK,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,QAAQ,EAAE;wCAAG;;;;;;;8CAEzE,8OAAC,kIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAKrB,8OAAC;4BAAI,WAAU;sCACZ,KAAK,MAAM,KAAK,kBACf,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;qDAGvC;;oCACG,KAAK,GAAG,CAAC,CAAC,qBACT,8OAAC;4CAAkB,WAAU;;8DAC3B,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KACE,KAAK,IAAI,CAAC,KAAK,EAAE,WAAW,UACxB,KAAK,IAAI,CAAC,KAAK,GACf,GAAG,8DAAwC,2BAA2B,KAAK,IAAI,CAAC,KAAK,EAAE,WAAW,OAAO,KAAK,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,IAAI,iCAAiC;oDAElM,KAAK,KAAK,IAAI,CAAC,IAAI;oDACnB,OAAO;oDACP,QAAQ;oDACR,WAAU;oDACV,SAAS,CAAC;wDACR,MAAM,SAAS,EAAE,MAAM;wDACvB,OAAO,GAAG,GAAG;oDACf;;;;;;8DAEF,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAoC,KAAK,IAAI,CAAC,IAAI;;;;;;sEAChE,8OAAC;4DAAE,WAAU;;8EACX,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAChB,KAAK,IAAI,CAAC,SAAS;gEAAC;;;;;;;;;;;;;8DAGzB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,mBAAmB,KAAK,MAAM,EAAE,KAAK,QAAQ,GAAG;sEAE/D,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;sEAEnB,8OAAC;4DAAK,WAAU;sEAAmB,KAAK,QAAQ;;;;;;sEAChD,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,mBAAmB,KAAK,MAAM,EAAE,KAAK,QAAQ,GAAG;sEAE/D,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;;;;;;;;;;;;8DAGpB,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,SAAS,IAAM,eAAe,KAAK,MAAM;8DAC1C;;;;;;;2CA5CO,KAAK,EAAE;;;;;kDAkDnB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;oDAAK,WAAU;;sEACd,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAChB;wDAAoB;;;;;;;;;;;;;;;;;;;;;;;;;sCAQjC,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,YAAY;8CAAQ;;;;;;gCAG5D,KAAK,MAAM,GAAG,mBACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;wCACP,YAAY;wCACZ,OAAO,IAAI,CAAC;oCACd;oCACA,WAAU;;sDAEV,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BASnD,8OAAC,2IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAqB,cAAc;0BACpD,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,8OAAC,2IAAA,CAAA,yBAAsB;;wCAAC;sDACmC,8OAAC;;gDAAQ,SAAS,YAAY;gDAAS;;;;;;;wCAAe;sDAC/G,8OAAC;;;;;sDACD,8OAAC;;;;;sDACD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;4DAAM,SAAS;4DAAK;4DAAG;;;;;;;kEACxB,8OAAC;;4DAAM,SAAS,YAAY;4DAAS;;;;;;;;;;;;;;;;;;sDAGzC,8OAAC;;;;;wCAAK;;;;;;;;;;;;;sCAIV,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,+BACC;;0DACE,8OAAC;gDAAI,WAAU;;;;;;4CAAsF;;uDAIvG;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB;uCAEe", "debugId": null}}]}